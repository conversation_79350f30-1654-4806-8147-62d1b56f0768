# CDM AI Agent Mock模式使用说明

## 概述

CDM AI Agent现已支持Mock模式，可以在没有真实华为云账号密码的情况下进行交互式测试。通过假token和mock API服务，完全模拟真实的华为云CDM API交互体验。

## 快速开始

### 1. 启动Mock服务器

```bash
# 在项目根目录下运行
python cdm_api\mock_server.py
```

服务器将在 `http://localhost:8000` 启动

### 2. 验证Mock集成

```bash
# 运行集成测试，验证所有功能正常
python test_mock_integration.py
```

### 3. 开始交互式测试

```bash
# 启动AI助手交互界面
python interactive_test.py
```

## 功能完整性

### ✅ 已支持的CDM操作 (95%覆盖率)

#### 🏢 集群管理 (10/10 完全覆盖)
- `list_clusters` - 查询集群列表
- `get_cluster` - 获取集群详情  
- `create_cluster` - 创建集群
- `delete_cluster` - 删除集群
- `restart_cluster` - 重启集群
- `start_cluster` - 启动集群
- `stop_cluster` - 停止集群
- `wait_cluster_ready` - 等待集群就绪
- `get_cluster_versions` - 获取支持版本
- `get_cluster_flavors` - 获取集群规格

#### 🔗 连接管理 (5/5 完全覆盖)
- `list_connections` - 查询连接列表
- `get_connection` - 获取连接详情
- `create_mysql_connection` - 创建MySQL连接
- `update_connection` - 更新连接配置
- `delete_connection` - 删除连接

#### 📋 作业管理 (8/9 基本覆盖)
- `list_jobs` - 查询作业列表
- `get_job` - 获取作业详情
- `create_data_migration_job` - 创建数据迁移作业
- `delete_job` - 删除作业
- `start_job` - 启动作业
- `stop_job` - 停止作业  
- `get_job_status` - 查询作业状态
- `get_job_executions` - 查询执行历史

## 使用示例

### 基本对话示例

```
您: 显示所有的CDM集群
助手: 当前共有3个CDM集群：
1. cdm-cluster-01 (ID: cluster-001, 状态: 正常)
2. cdm-cluster-02 (ID: cluster-002, 状态: 正常)  
3. cdm-cluster-03 (ID: cluster-003, 状态: 正常)

您: 查看cluster-001的详细信息
助手: 集群cluster-001详细信息：
- 名称: cdm-cluster-01
- 状态: 200 (正常)
- 模式: 分片模式
- 规格: CDM Medium (4核16GB)
- 版本: 2.9.2.200
- 可用区: cn-north-1a

您: 列出cluster-001上的所有连接
助手: cluster-001上的连接列表：
1. mysql-connection (MySQL数据库)
2. dws-connection (DWS数据仓库)
3. obs-connection (对象存储)

您: 创建一个从MySQL到DWS的数据迁移作业
助手: 我将为您创建数据迁移作业，请提供以下信息：
- 作业名称
- 源表名称  
- 目标表名称
...
```

### 高级操作示例

```
您: 重启cluster-001集群
助手: 正在重启集群cluster-001...
✓ 重启命令已发送，集群将在几分钟内重启完成

您: 等待集群cluster-001就绪
助手: 正在等待集群就绪...
✓ 集群cluster-001已就绪，可以正常使用

您: 停止作业mysql2dws-job
助手: ✓ 作业mysql2dws-job已成功停止

您: 查看作业mysql2dws-job的执行历史  
助手: mysql2dws-job执行历史：
1. 执行ID: xxx-xxx-xxx (状态: 成功, 处理1000条记录)
2. 执行ID: yyy-yyy-yyy (状态: 运行中, 进度50%)
```

## 技术实现

### Mock认证机制
- 使用固定假token: `mock-token-12345678`  
- 任何token都被认为是有效的
- 完全绕过华为云IAM认证流程

### Mock数据
- 模拟3个集群 (cluster-001, cluster-002, cluster-003)
- 每个集群有3种类型的连接
- 每个集群有3个示例作业
- 支持所有CRUD操作的模拟响应

### 状态模拟
- 集群状态: 正常(200)、创建中(100)、故障(300)
- 作业状态: 运行中、成功、失败、已停止
- 执行历史: 自动生成模拟执行记录

## 测试文件

1. **test_mock_integration.py** - 基础集成测试
2. **test_new_functions.py** - 新增功能测试  
3. **function_coverage_report.py** - 功能覆盖报告
4. **interactive_test.py** - 交互式AI助手

## 注意事项

1. **数据持久性**: Mock数据仅在内存中，重启服务器后丢失
2. **网络要求**: 需要本地8000端口可用
3. **编码问题**: 在Windows命令行中可能出现中文显示问题
4. **功能限制**: 不支持实际的集群创建和数据传输

## 故障排除

### Mock服务器无法启动
```bash
# 检查端口占用
netstat -an | findstr 8000

# 使用不同端口启动
uvicorn cdm_api.mock_server:app --host 0.0.0.0 --port 8001
```

### 连接超时错误
```bash
# 确认服务器正在运行
curl http://localhost:8000

# 检查防火墙设置
```

### 中文显示问题  
```bash
# 设置控制台编码为UTF-8
chcp 65001
```

## 扩展开发

### 添加新的Mock API
1. 在 `mock_server.py` 中添加新的端点
2. 在 `tools.py` 中添加对应的工具函数
3. 更新函数定义列表
4. 实现执行逻辑

### 自定义Mock数据
修改 `mock_server.py` 中的数据生成函数：
- `get_mock_cluster_data()`
- `get_mock_connection_data()`
- `get_mock_job_data()`

---

🎉 **现在您可以完全无障碍地测试和演示CDM AI Agent的所有功能！**
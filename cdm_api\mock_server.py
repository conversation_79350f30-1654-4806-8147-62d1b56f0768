"""
华为云CDM API MOCK服务
基于华为云官方API文档的请求和响应示例构造的测试服务
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from uuid import uuid4
import json
import time

from fastapi import FastAPI, HTTPException, Request, status, Header, Depends
from fastapi.responses import JSONResponse
import uvicorn
from pydantic import BaseModel
from typing import Optional


app = FastAPI(
    title="华为云CDM API Mock Server",
    description="用于测试的CDM API模拟服务",
    version="1.0.0"
)


def verify_token(x_auth_token: Optional[str] = Header(None)):
    """
    验证token（Mock模式下接受任何token）
    """
    if not x_auth_token:
        raise HTTPException(
            status_code=401,
            detail={
                "error_code": "CDM.0401",
                "error_msg": "Missing X-Auth-Token header",
                "request_id": str(uuid4())
            }
        )
    # Mock模式下，任何token都被认为是有效的
    return x_auth_token

# 模拟数据存储
mock_clusters = {}
mock_connections = {}
mock_jobs = {}
mock_job_executions = {}


class MockResponse(BaseModel):
    """Mock响应基类"""
    pass


# 集群相关Mock响应
def get_mock_cluster_data(cluster_id: str = None) -> Dict[str, Any]:
    """获取集群mock数据"""
    if not cluster_id:
        cluster_id = str(uuid4())
    
    return {
        "cluster": {
            "id": cluster_id,
            "name": "cdm-cluster-01",
            "status": "200",
            "statusDetail": "Normal",
            "created": (datetime.now() - timedelta(hours=2)).isoformat() + "Z",
            "updated": datetime.now().isoformat() + "Z",
            "mode": "sharding",
            "flavor": {
                "id": "cdm.medium",
                "name": "CDM Medium",
                "cpu": "4",
                "memory": "16",
                "disk": "100"
            },
            "datastore": {
                "type": "cdm",
                "version": "2.9.2.200"
            },
            "instances": [
                {
                    "id": f"instance-{i}",
                    "name": f"cdm-cluster-01-{i}",
                    "role": "master" if i == 0 else "worker",
                    "status": "200",
                    "flavor": "cdm.medium"
                } for i in range(3)
            ],
            "publicEndpoint": f"cdm-{cluster_id[:8]}.example.com",
            "endpoint": f"192.168.1.10{cluster_id[-1]}",
            "securityGroupId": "sg-12345678",
            "subnetId": "subnet-12345678",
            "vpcId": "vpc-12345678",
            "availabilityZone": "cn-north-1a",
            "enterpriseProjectId": "0"
        }
    }


def get_mock_connection_data(connection_name: str = None) -> Dict[str, Any]:
    """获取连接mock数据"""
    if not connection_name:
        connection_name = "mysql-connection"
    
    return {
        "link": {
            "name": connection_name,
            "connector-name": "generic-jdbc-connector",
            "link-config-values": {
                "configs": [
                    {
                        "inputs": [
                            {"name": "linkConfig.databaseType", "value": "MYSQL"},
                            {"name": "linkConfig.host", "value": "*************"},
                            {"name": "linkConfig.port", "value": "3306"},
                            {"name": "linkConfig.database", "value": "testdb"},
                            {"name": "linkConfig.username", "value": "root"},
                            {"name": "linkConfig.password", "value": "******"}
                        ],
                        "name": "linkConfig"
                    }
                ]
            },
            "creation-user": "admin",
            "creation-date": (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
            "update-user": "admin", 
            "update-date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "enabled": True
        }
    }


def get_mock_job_data(job_name: str = None) -> Dict[str, Any]:
    """获取作业mock数据"""
    if not job_name:
        job_name = "mysql2dws-job"
    
    return {
        "job": {
            "name": job_name,
            "type": "NORMAL_JOB",
            "from-connector-name": "generic-jdbc-connector",
            "to-connector-name": "generic-jdbc-connector",
            "from-link-name": "mysql-connection",
            "to-link-name": "dws-connection",
            "from-config-values": {
                "configs": [
                    {
                        "inputs": [
                            {"name": "fromJobConfig.useSql", "value": "false"},
                            {"name": "fromJobConfig.schemaName", "value": "testdb"},
                            {"name": "fromJobConfig.tableName", "value": "users"},
                            {"name": "fromJobConfig.columnList", "value": "id&name&email&created_at"},
                            {"name": "fromJobConfig.incrMigration", "value": "false"}
                        ],
                        "name": "fromJobConfig"
                    }
                ]
            },
            "to-config-values": {
                "configs": [
                    {
                        "inputs": [
                            {"name": "toJobConfig.schemaName", "value": "public"},
                            {"name": "toJobConfig.tablePreparation", "value": "DROP_AND_CREATE"},
                            {"name": "toJobConfig.tableName", "value": "users_dws"}
                        ],
                        "name": "toJobConfig"
                    }
                ]
            },
            "driver-config-values": {
                "configs": [
                    {
                        "inputs": [
                            {"name": "driverConfig.numExtractors", "value": "1"},
                            {"name": "driverConfig.numLoaders", "value": "1"}
                        ],
                        "name": "driverConfig"
                    }
                ]
            },
            "creation-user": "admin",
            "creation-date": (datetime.now() - timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S"),
            "update-user": "admin",
            "update-date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "enabled": True
        }
    }


def get_mock_job_execution_data() -> Dict[str, Any]:
    """获取作业执行mock数据"""
    execution_id = str(uuid4())
    return {
        "submission": {
            "submission-id": execution_id,
            "job-name": "mysql2dws-job", 
            "status": "SUCCEEDED",
            "creation-date": (datetime.now() - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S"),
            "last-update-date": (datetime.now() - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:%S"),
            "counters": {
                "org.apache.sqoop.submission.counter.SqloopCounters": {
                    "BYTES_READ": 1024000,
                    "TOTAL_RECORDS_READ": 1000,
                    "BYTES_WRITTEN": 1024000,
                    "TOTAL_RECORDS_WRITTEN": 1000
                }
            },
            "progress": 100.0
        }
    }


# API端点实现

@app.get("/")
async def root():
    return {
        "message": "华为云CDM API Mock服务",
        "version": "1.0.0", 
        "docs": "/docs"
    }


# 集群管理API
@app.get("/v1.1/{project_id}/clusters")
async def list_clusters(project_id: str, offset: int = 0, limit: int = 100, token: str = Depends(verify_token)):
    """查询集群列表"""
    clusters = []
    for i in range(min(3, limit)):  # 模拟返回3个集群
        cluster_id = f"cluster-{i+1:03d}"
        cluster_data = get_mock_cluster_data(cluster_id)
        clusters.append(cluster_data["cluster"])
    
    return {
        "clusters": clusters,
        "total": 3
    }


@app.get("/v1.1/{project_id}/clusters/{cluster_id}")
async def get_cluster(project_id: str, cluster_id: str, token: str = Depends(verify_token)):
    """查询集群详情"""
    return get_mock_cluster_data(cluster_id)


@app.post("/v1.1/{project_id}/clusters")
async def create_cluster(project_id: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """创建集群"""
    cluster_id = str(uuid4())
    mock_clusters[cluster_id] = {
        **request,
        "id": cluster_id,
        "status": "100",  # 创建中
        "created": datetime.now().isoformat() + "Z"
    }
    
    return {"id": cluster_id}


@app.delete("/v1.1/{project_id}/clusters/{cluster_id}")
async def delete_cluster(project_id: str, cluster_id: str, token: str = Depends(verify_token)):
    """删除集群"""
    if cluster_id in mock_clusters:
        del mock_clusters[cluster_id]
    return {"message": "Cluster deleted successfully"}


@app.post("/v1.1/{project_id}/clusters/{cluster_id}/action")
async def cluster_action(project_id: str, cluster_id: str, action: Dict[str, Any], token: str = Depends(verify_token)):
    """集群操作（重启/启动/停止）"""
    action_type = list(action.keys())[0]
    job_id = f"job-{str(uuid4())[:8]}"
    
    return {
        "jobId": job_id,
        "message": f"Cluster {action_type} operation initiated"
    }


# 连接管理API
@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/link")
async def list_connections(project_id: str, cluster_id: str, token: str = Depends(verify_token)):
    """查询连接列表"""
    links = []
    connection_types = ["mysql-connection", "dws-connection", "obs-connection"]
    
    for conn_name in connection_types:
        link_data = get_mock_connection_data(conn_name)
        links.append(link_data["link"])
    
    return {"links": links}


@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}")
async def get_connection(project_id: str, cluster_id: str, connection_name: str, token: str = Depends(verify_token)):
    """查询连接详情"""
    return get_mock_connection_data(connection_name)


@app.post("/v1.1/{project_id}/clusters/{cluster_id}/cdm/link")
async def create_connection(project_id: str, cluster_id: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """创建连接"""
    connections = request.get("links", [])
    created_connections = []
    
    for conn in connections:
        conn_name = conn.get("name")
        mock_connections[conn_name] = conn
        created_connections.append(conn_name)
    
    return {
        "message": f"Created {len(created_connections)} connections successfully",
        "connections": created_connections
    }


@app.put("/v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}")
async def update_connection(project_id: str, cluster_id: str, connection_name: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """更新连接"""
    if connection_name in mock_connections:
        mock_connections[connection_name].update(request.get("links", [{}])[0])
    
    return {"message": f"Connection {connection_name} updated successfully"}


@app.delete("/v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}")
async def delete_connection(project_id: str, cluster_id: str, connection_name: str, token: str = Depends(verify_token)):
    """删除连接"""
    if connection_name in mock_connections:
        del mock_connections[connection_name]
    
    return {"message": f"Connection {connection_name} deleted successfully"}


# 作业管理API
@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job")
async def list_jobs(project_id: str, cluster_id: str, token: str = Depends(verify_token)):
    """查询作业列表"""
    jobs = []
    job_names = ["mysql2dws-job", "obs2mysql-job", "hive2dws-job"]
    
    for job_name in job_names:
        job_data = get_mock_job_data(job_name)
        jobs.append(job_data["job"])
    
    return {"jobs": jobs}


@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}")
async def get_job(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """查询作业详情"""
    return get_mock_job_data(job_name)


@app.post("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job")
async def create_job(project_id: str, cluster_id: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """创建作业"""
    jobs = request.get("jobs", [])
    created_jobs = []
    
    for job in jobs:
        job_name = job.get("name")
        mock_jobs[job_name] = job
        created_jobs.append(job_name)
    
    return {
        "message": f"Created {len(created_jobs)} jobs successfully",
        "jobs": created_jobs
    }


@app.put("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}")
async def update_job(project_id: str, cluster_id: str, job_name: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """更新作业"""
    if job_name in mock_jobs:
        mock_jobs[job_name].update(request.get("jobs", [{}])[0])
    
    return {"message": f"Job {job_name} updated successfully"}


@app.delete("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}")
async def delete_job(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """删除作业"""
    if job_name in mock_jobs:
        del mock_jobs[job_name]
    
    return {"message": f"Job {job_name} deleted successfully"}


@app.put("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/start")
async def start_job(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """启动作业"""
    submission_id = str(uuid4())
    mock_job_executions[submission_id] = {
        "job_name": job_name,
        "status": "RUNNING",
        "start_time": datetime.now(),
        "progress": 0.0
    }
    
    return {"submission-id": submission_id}


@app.put("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/stop")
async def stop_job(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """停止作业"""
    # 找到该作业的执行记录并更新状态
    for exec_id, execution in mock_job_executions.items():
        if execution["job_name"] == job_name and execution["status"] == "RUNNING":
            execution["status"] = "KILLED"
            execution["end_time"] = datetime.now()
            break
    
    return {"message": f"Job {job_name} stopped successfully"}


@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/status")
async def get_job_status(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """查询作业状态"""
    # 查找最新的执行记录
    latest_execution = None
    for execution in mock_job_executions.values():
        if execution["job_name"] == job_name:
            if not latest_execution or execution["start_time"] > latest_execution["start_time"]:
                latest_execution = execution
    
    if latest_execution:
        return {
            "status": latest_execution["status"],
            "progress": latest_execution.get("progress", 0.0),
            "message": f"Job {job_name} is {latest_execution['status'].lower()}"
        }
    else:
        return {
            "status": "NEVER_EXECUTED",
            "progress": 0.0,
            "message": f"Job {job_name} has never been executed"
        }


@app.get("/v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/submissions")
async def get_job_executions(project_id: str, cluster_id: str, job_name: str, token: str = Depends(verify_token)):
    """查询作业执行历史"""
    submissions = []
    
    for exec_id, execution in mock_job_executions.items():
        if execution["job_name"] == job_name:
            submission = get_mock_job_execution_data()
            submission["submission"]["submission-id"] = exec_id
            submission["submission"]["job-name"] = job_name
            submission["submission"]["status"] = execution["status"]
            submissions.append(submission["submission"])
    
    # 如果没有执行记录，返回一个示例
    if not submissions:
        sample_submission = get_mock_job_execution_data()
        sample_submission["submission"]["job-name"] = job_name
        submissions.append(sample_submission["submission"])
    
    return {"submissions": submissions}


# 随机集群作业操作
@app.post("/v1.1/{project_id}/clusters/job")
async def create_and_start_random_cluster_job(project_id: str, request: Dict[str, Any], token: str = Depends(verify_token)):
    """随机集群创建并启动作业"""
    job = request.get("jobs", [{}])[0]
    job_name = job.get("name", "random-job")
    submission_id = str(uuid4())
    
    mock_jobs[job_name] = job
    mock_job_executions[submission_id] = {
        "job_name": job_name,
        "status": "RUNNING", 
        "start_time": datetime.now(),
        "progress": 0.0
    }
    
    return {"submission-id": submission_id}


@app.put("/v1.1/{project_id}/clusters/job/{job_name}/stop")
async def stop_random_cluster_job(project_id: str, job_name: str, token: str = Depends(verify_token)):
    """停止随机集群作业"""
    for exec_id, execution in mock_job_executions.items():
        if execution["job_name"] == job_name and execution["status"] == "RUNNING":
            execution["status"] = "KILLED"
            execution["end_time"] = datetime.now()
            break
    
    return {"message": f"Random cluster job {job_name} stopped successfully"}


# 辅助API
@app.get("/v1.1/{project_id}/clusters/versions")
async def get_cluster_versions(project_id: str, token: str = Depends(verify_token)):
    """获取支持的集群版本列表"""
    return {
        "versions": [
            "2.9.2.200",
            "2.9.2.100",
            "2.9.1.300"
        ]
    }


@app.get("/v1.1/{project_id}/clusters/flavors")
async def get_cluster_flavors(project_id: str, token: str = Depends(verify_token)):
    """获取集群规格列表"""
    return {
        "flavors": [
            {
                "id": "cdm.small",
                "name": "CDM Small",
                "cpu": "2",
                "memory": "8",
                "disk": "50"
            },
            {
                "id": "cdm.medium", 
                "name": "CDM Medium",
                "cpu": "4",
                "memory": "16",
                "disk": "100"
            },
            {
                "id": "cdm.large",
                "name": "CDM Large", 
                "cpu": "8",
                "memory": "32",
                "disk": "200"
            }
        ]
    }


# 错误处理
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error_code": "CDM.0001",
            "error_msg": f"Endpoint not found: {request.url.path}",
            "request_id": str(uuid4())
        }
    )


@app.exception_handler(500)
async def server_error_handler(request: Request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "error_code": "CDM.9999",
            "error_msg": "Internal server error",
            "request_id": str(uuid4())
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "mock_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
"""
CDM MCP服务器实现
"""
import asyncio
from typing import Any, Dict, List, Sequence

try:
    import mcp
    import mcp.server.stdio
    import mcp.types as types
    MCP_AVAILABLE = True
    
    # 检查可用的Server类
    MCP_SERVER_CLASS = None
    if hasattr(mcp, 'Server'):
        MCP_SERVER_CLASS = mcp.Server
    elif hasattr(mcp.server, 'Server'):
        MCP_SERVER_CLASS = mcp.server.Server
    elif hasattr(mcp.server.stdio, 'Server'):
        MCP_SERVER_CLASS = mcp.server.stdio.Server
    else:
        MCP_AVAILABLE = False
        logger.warning("无法找到兼容的MCP Server类")
        
except ImportError as e:
    logger.warning(f"MCP库未安装或版本不兼容: {e}")
    MCP_AVAILABLE = False
    MCP_SERVER_CLASS = None
from loguru import logger

from cdm_api.client import CDMClient
from .tools import CDMTools


class CDMMCPServer:
    """CDM MCP服务器"""
    
    def __init__(self):
        if not MCP_AVAILABLE or not MCP_SERVER_CLASS:
            raise ImportError("MCP库未安装或版本不兼容，无法启动MCP服务器")
        
        try:
            self.server = MCP_SERVER_CLASS("cdm-agent")
            logger.info(f"使用MCP Server类: {MCP_SERVER_CLASS.__name__}")
        except Exception as e:
            raise ImportError(f"无法创建MCP服务器实例: {e}")
                
        self.cdm_client = None
        self.tools = None
        
        # 注册处理器
        self._register_handlers()
        
    def _register_handlers(self):
        """注册MCP处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """列出可用工具"""
            if not self.tools:
                await self._initialize_client()
            return self.tools.get_tool_definitions()
            
        @self.server.call_tool()
        async def handle_call_tool(
            name: str, arguments: Dict[str, Any]
        ) -> List[types.TextContent]:
            """调用工具"""
            if not self.tools:
                await self._initialize_client()
            return await self.tools.call_tool(name, arguments)
            
        @self.server.list_resources()
        async def handle_list_resources() -> List[types.Resource]:
            """列出可用资源"""
            return [
                types.Resource(
                    uri="cdm://clusters",
                    name="CDM集群列表",
                    description="获取所有CDM集群信息",
                    mimeType="application/json"
                ),
                types.Resource(
                    uri="cdm://connections",
                    name="CDM连接列表",
                    description="获取所有CDM连接信息",
                    mimeType="application/json"
                ),
                types.Resource(
                    uri="cdm://jobs",
                    name="CDM作业列表", 
                    description="获取所有CDM作业信息",
                    mimeType="application/json"
                ),
                types.Resource(
                    uri="cdm://cluster-versions",
                    name="CDM集群版本",
                    description="获取支持的CDM集群版本",
                    mimeType="application/json"
                ),
                types.Resource(
                    uri="cdm://cluster-flavors",
                    name="CDM集群规格",
                    description="获取CDM集群规格信息",
                    mimeType="application/json"
                )
            ]
            
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """读取资源"""
            if not self.tools:
                await self._initialize_client()
                
            if uri == "cdm://clusters":
                result = await self.tools.call_tool("cdm_list_clusters", {})
                return result[0].text
                
            elif uri == "cdm://cluster-versions":
                result = await self.tools.call_tool("cdm_get_cluster_versions", {})
                return result[0].text
                
            elif uri == "cdm://cluster-flavors":
                result = await self.tools.call_tool("cdm_get_cluster_flavors", {})
                return result[0].text
                
            elif uri.startswith("cdm://clusters/") and uri.endswith("/connections"):
                # 提取集群ID，格式: cdm://clusters/{cluster_id}/connections
                cluster_id = uri.split("/")[3]
                result = await self.tools.call_tool("cdm_list_connections", {"cluster_id": cluster_id})
                return result[0].text
                
            elif uri.startswith("cdm://clusters/") and uri.endswith("/jobs"):
                # 提取集群ID，格式: cdm://clusters/{cluster_id}/jobs
                cluster_id = uri.split("/")[3]
                result = await self.tools.call_tool("cdm_list_jobs", {"cluster_id": cluster_id})
                return result[0].text
                
            else:
                raise ValueError(f"Unknown resource URI: {uri}")
                
        @self.server.list_prompts()
        async def handle_list_prompts() -> List[types.Prompt]:
            """列出可用提示模板"""
            return [
                types.Prompt(
                    name="create_cluster",
                    description="创建CDM集群的提示模板",
                    arguments=[
                        types.PromptArgument(
                            name="name",
                            description="集群名称",
                            required=True
                        ),
                        types.PromptArgument(
                            name="mode", 
                            description="集群模式 (sharding/simple)",
                            required=True
                        ),
                        types.PromptArgument(
                            name="version",
                            description="集群版本",
                            required=True
                        )
                    ]
                ),
                types.Prompt(
                    name="create_migration_job",
                    description="创建数据迁移作业的提示模板",
                    arguments=[
                        types.PromptArgument(
                            name="cluster_id",
                            description="集群ID",
                            required=True
                        ),
                        types.PromptArgument(
                            name="source_type",
                            description="源数据库类型",
                            required=True
                        ),
                        types.PromptArgument(
                            name="target_type",
                            description="目标数据库类型",
                            required=True
                        )
                    ]
                ),
                types.Prompt(
                    name="monitor_job",
                    description="监控作业执行状态的提示模板",
                    arguments=[
                        types.PromptArgument(
                            name="cluster_id",
                            description="集群ID",
                            required=True
                        ),
                        types.PromptArgument(
                            name="job_name",
                            description="作业名称",
                            required=True
                        )
                    ]
                )
            ]
            
        @self.server.get_prompt()
        async def handle_get_prompt(
            name: str, arguments: Dict[str, str]
        ) -> types.GetPromptResult:
            """获取提示模板"""
            if name == "create_cluster":
                cluster_name = arguments.get("name", "my-cluster")
                mode = arguments.get("mode", "simple")
                version = arguments.get("version", "2.9.0")
                
                prompt = f"""
创建CDM集群：

集群名称: {cluster_name}
集群模式: {mode}
集群版本: {version}

请提供以下信息来完成集群创建：
1. 规格ID (flavor_id) - 选择合适的计算规格
2. 节点数量 (num_instances) - 根据数据量和性能需求
3. 可用区 (availability_zone) - 选择部署区域
4. VPC ID (vpc_id) - 虚拟私有云ID
5. 子网ID (subnet_id) - 子网ID
6. 安全组ID (security_group_id) - 安全组配置
7. 是否绑定公网IP (public_ip) - true/false

使用工具: cdm_create_cluster
"""
                
                return types.GetPromptResult(
                    description=f"创建名为 {cluster_name} 的CDM集群",
                    messages=[
                        types.PromptMessage(
                            role="user",
                            content=types.TextContent(type="text", text=prompt)
                        )
                    ]
                )
                
            elif name == "create_migration_job":
                cluster_id = arguments.get("cluster_id")
                source_type = arguments.get("source_type", "MySQL")
                target_type = arguments.get("target_type", "OBS")
                
                prompt = f"""
创建数据迁移作业：

集群ID: {cluster_id}
源数据类型: {source_type}
目标数据类型: {target_type}

请配置以下信息：
1. 作业名称
2. 源连接器配置 (from_config_values)
3. 目标连接器配置 (to_config_values)
4. 驱动配置 (driver_config_values) - 可选

使用工具: cdm_create_job
"""
                
                return types.GetPromptResult(
                    description=f"在集群 {cluster_id} 上创建从 {source_type} 到 {target_type} 的迁移作业",
                    messages=[
                        types.PromptMessage(
                            role="user", 
                            content=types.TextContent(type="text", text=prompt)
                        )
                    ]
                )
                
            elif name == "monitor_job":
                cluster_id = arguments.get("cluster_id")
                job_name = arguments.get("job_name")
                
                prompt = f"""
监控作业执行状态：

集群ID: {cluster_id}
作业名称: {job_name}

可以使用以下工具进行监控：
1. cdm_get_job_status - 获取作业当前状态
2. cdm_get_job_executions - 查看作业执行历史
3. cdm_get_job - 查看作业详细配置

监控要点：
- 作业执行状态 (BOOTING/RUNNING/SUCCEEDED/FAILED/KILLED)
- 执行进度百分比
- 数据传输计数器
- 错误信息分析
"""
                
                return types.GetPromptResult(
                    description=f"监控集群 {cluster_id} 上作业 {job_name} 的执行状态",
                    messages=[
                        types.PromptMessage(
                            role="user",
                            content=types.TextContent(type="text", text=prompt)
                        )
                    ]
                )
            else:
                raise ValueError(f"Unknown prompt: {name}")
                
    async def _initialize_client(self):
        """初始化CDM客户端和工具"""
        if not self.cdm_client:
            self.cdm_client = CDMClient()
            self.tools = CDMTools(self.cdm_client)
            logger.info("CDM客户端和工具初始化完成")
            
    async def run(self):
        """运行MCP服务器"""
        logger.info("启动CDM MCP服务器...")
        
        # 初始化客户端
        await self._initialize_client()
        
        # 运行服务器
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                self.server.create_initialization_options()
            )


async def main():
    """主函数"""
    server = CDMMCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
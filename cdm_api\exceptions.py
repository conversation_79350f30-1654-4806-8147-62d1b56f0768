"""
CDM API异常定义
"""
import traceback
from typing import Optional, Any, Dict
from loguru import logger


class CDMError(Exception):
    """CDM基础异常"""
    def __init__(self, message: str, cause: Exception = None, **kwargs):
        self.message = message
        self.cause = cause
        self.extra_info = kwargs
        
        # 记录完整的错误堆栈信息
        if cause:
            self.traceback = traceback.format_exception(type(cause), cause, cause.__traceback__)
            logger.error(f"CDMError: {message}")
            logger.error(f"Caused by: {cause}")
            logger.error("Full traceback:")
            logger.error(''.join(self.traceback))
        else:
            self.traceback = traceback.format_stack()
            logger.error(f"CDMError: {message}")
            logger.error("Stack trace:")
            logger.error(''.join(self.traceback))
            
        super().__init__(message)
    
    def get_full_error_info(self) -> Dict[str, Any]:
        """获取完整的错误信息"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "cause": str(self.cause) if self.cause else None,
            "traceback": self.traceback,
            "extra_info": self.extra_info
        }


class CDMAuthError(CDMError):
    """CDM认证异常"""
    def __init__(self, message: str, auth_endpoint: str = None, username: str = None, cause: Exception = None):
        super().__init__(
            message, 
            cause=cause,
            auth_endpoint=auth_endpoint,
            username=username
        )
        self.auth_endpoint = auth_endpoint
        self.username = username


class CDMAPIError(CDMError):
    """CDM API调用异常"""
    def __init__(self, message: str, status_code: int = None, error_code: str = None, 
                 request_url: str = None, response_body: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            status_code=status_code,
            error_code=error_code,
            request_url=request_url,
            response_body=response_body
        )
        self.status_code = status_code
        self.error_code = error_code
        self.request_url = request_url
        self.response_body = response_body
        
        # 记录详细的API错误信息
        logger.error(f"CDM API Error: {message}")
        if request_url:
            logger.error(f"Request URL: {request_url}")
        if status_code:
            logger.error(f"Status Code: {status_code}")
        if error_code:
            logger.error(f"Error Code: {error_code}")
        if response_body:
            logger.error(f"Response Body: {response_body}")


class CDMConfigError(CDMError):
    """CDM配置异常"""
    def __init__(self, message: str, config_key: str = None, config_value: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            config_key=config_key,
            config_value=config_value
        )
        self.config_key = config_key
        self.config_value = config_value


class CDMTimeoutError(CDMError):
    """CDM超时异常"""
    def __init__(self, message: str, timeout_seconds: int = None, operation: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            timeout_seconds=timeout_seconds,
            operation=operation
        )
        self.timeout_seconds = timeout_seconds
        self.operation = operation


class CDMConnectionError(CDMError):
    """CDM连接异常"""
    def __init__(self, message: str, connection_name: str = None, connector_type: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            connection_name=connection_name,
            connector_type=connector_type
        )
        self.connection_name = connection_name
        self.connector_type = connector_type


class CDMJobError(CDMError):
    """CDM作业异常"""
    def __init__(self, message: str, job_name: str = None, job_status: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            job_name=job_name,
            job_status=job_status
        )
        self.job_name = job_name
        self.job_status = job_status


class CDMClusterError(CDMError):
    """CDM集群异常"""
    def __init__(self, message: str, cluster_id: str = None, cluster_status: str = None, cause: Exception = None):
        super().__init__(
            message,
            cause=cause,
            cluster_id=cluster_id,
            cluster_status=cluster_status
        )
        self.cluster_id = cluster_id
        self.cluster_status = cluster_status


def handle_exception(func):
    """异常处理装饰器，确保所有异常都被正确记录"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except CDMError:
            # CDM异常已经在初始化时记录了堆栈信息，直接重新抛出
            raise
        except Exception as e:
            # 其他异常需要包装成CDM异常并记录堆栈
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            logger.error(f"Full traceback: {''.join(traceback.format_exception(type(e), e, e.__traceback__))}")
            raise CDMError(f"Unexpected error in {func.__name__}: {str(e)}", cause=e)
    
    return wrapper


def log_and_raise(exception_class, message: str, **kwargs):
    """记录错误并抛出指定类型的异常"""
    logger.error(f"Raising {exception_class.__name__}: {message}")
    for key, value in kwargs.items():
        if value is not None:
            logger.error(f"  {key}: {value}")
    
    # 记录调用堆栈
    stack = traceback.format_stack()[:-1]  # 排除当前函数
    logger.error("Call stack:")
    logger.error(''.join(stack))
    
    raise exception_class(message, **kwargs)
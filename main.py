"""
华为云CDM AI Agent主应用程序
"""
import asyncio
import sys
from pathlib import Path

from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
from loguru import logger

from config import config
from agent.core import CDMAgent
try:
    from mcp_server.server import CDMMCPServer
    MCP_AVAILABLE = True
except ImportError as e:
    logger.warning(f"MCP服务器不可用: {e}")
    MCP_AVAILABLE = False


# 配置日志
def setup_logging():
    logger.remove()
    log_format = "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    
    # 控制台日志
    logger.add(
        sys.stdout,
        format=log_format,
        level=config.log_level,
        colorize=True
    )
    
    # 文件日志
    if config.log_file:
        logger.add(
            config.log_file,
            format=log_format,
            level=config.log_level,
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )


# 全局Agent实例
cdm_agent: CDMAgent = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动
    global cdm_agent
    setup_logging()
    logger.info("启动CDM AI Agent...")
    
    try:
        cdm_agent = CDMAgent()
        logger.info("CDM Agent初始化完成")
    except Exception as e:
        logger.error(f"CDM Agent初始化失败: {e}")
        cdm_agent = None
    
    yield
    
    # 关闭
    logger.info("关闭CDM AI Agent...")


# FastAPI应用
app = FastAPI(
    title="CDM AI Agent",
    description="华为云CDM智能管理代理",
    version="1.0.0",
    lifespan=lifespan
)

# 静态文件服务（如果目录存在）
import os
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/")
async def get_index():
    """主页"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CDM AI Agent</title>
        <meta charset="utf-8">
        <style>
            body { 
                font-family: Arial, sans-serif; 
                max-width: 1000px; 
                margin: 0 auto; 
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container { 
                background: white; 
                padding: 30px; 
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .title {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 30px;
            }
            .chat-container {
                border: 1px solid #ddd;
                height: 400px;
                overflow-y: auto;
                padding: 15px;
                margin-bottom: 20px;
                background-color: #fafafa;
                border-radius: 5px;
            }
            .input-group {
                display: flex;
                gap: 10px;
            }
            .input-field {
                flex: 1;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }
            .btn {
                padding: 10px 20px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            }
            .btn:hover {
                background-color: #2980b9;
            }
            .message {
                margin-bottom: 10px;
                padding: 8px 12px;
                border-radius: 5px;
            }
            .user-message {
                background-color: #e3f2fd;
                text-align: right;
            }
            .agent-message {
                background-color: #f1f8e9;
            }
            .features {
                margin-top: 30px;
            }
            .feature-list {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }
            .feature-item {
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #3498db;
            }
            .status {
                text-align: center;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .status.connected {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .status.disconnected {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="title">🤖 华为云CDM AI Agent</h1>
            
            <div id="status" class="status disconnected">
                连接状态: 未连接
            </div>
            
            <div id="chatContainer" class="chat-container">
                <div class="message agent-message">
                    👋 您好！我是华为云CDM智能助手。我可以帮助您管理CDM集群、数据连接和迁移作业。
                    <br><br>
                    💡 您可以尝试说：
                    <br>• "列出所有集群"
                    <br>• "创建一个新集群"  
                    <br>• "查看集群详情"
                    <br>• "启动/停止集群"
                    <br>• "创建数据连接"
                    <br>• "开始数据迁移"
                </div>
            </div>
            
            <div class="input-group">
                <input type="text" id="messageInput" class="input-field" 
                       placeholder="请输入您的指令..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" class="btn" id="sendBtn">发送</button>
            </div>
            
            <div class="features">
                <h2>🚀 功能特性</h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <h3>🏗️ 集群管理</h3>
                        <p>创建、删除、启动、停止CDM集群，支持多种规格和版本选择</p>
                    </div>
                    <div class="feature-item">
                        <h3>🔗 连接管理</h3>
                        <p>管理各种数据源连接，支持MySQL、Oracle、OBS、HDFS等</p>
                    </div>
                    <div class="feature-item">
                        <h3>⚡ 作业管理</h3>
                        <p>创建和管理数据迁移作业，支持实时监控和历史查询</p>
                    </div>
                    <div class="feature-item">
                        <h3>🧠 智能对话</h3>
                        <p>自然语言交互，无需记住复杂的API参数和命令</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let ws = null;
            let isConnected = false;

            function connectWebSocket() {
                const wsUrl = `ws://${window.location.host}/ws`;
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    updateConnectionStatus();
                    console.log('WebSocket连接已建立');
                };

                ws.onmessage = function(event) {
                    addMessage(event.data, 'agent');
                };

                ws.onclose = function(event) {
                    isConnected = false;
                    updateConnectionStatus();
                    console.log('WebSocket连接已关闭');
                    // 尝试重连
                    setTimeout(connectWebSocket, 3000);
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    addMessage('连接错误，请刷新页面重试', 'agent');
                };
            }

            function updateConnectionStatus() {
                const statusEl = document.getElementById('status');
                if (isConnected) {
                    statusEl.textContent = '连接状态: 已连接';
                    statusEl.className = 'status connected';
                } else {
                    statusEl.textContent = '连接状态: 未连接';
                    statusEl.className = 'status disconnected';
                }
            }

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (!message) return;
                if (!isConnected) {
                    alert('连接未建立，请稍后重试');
                    return;
                }

                addMessage(message, 'user');
                ws.send(message);
                input.value = '';
                
                // 禁用发送按钮
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = true;
                sendBtn.textContent = '处理中...';
                
                setTimeout(() => {
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送';
                }, 2000);
            }

            function addMessage(message, sender) {
                const chatContainer = document.getElementById('chatContainer');
                const messageEl = document.createElement('div');
                messageEl.className = `message ${sender}-message`;
                messageEl.innerHTML = message.replace(/\\n/g, '<br>');
                chatContainer.appendChild(messageEl);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }

            // 页面加载时连接WebSocket
            window.onload = function() {
                connectWebSocket();
            };
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket聊天端点"""
    await websocket.accept()
    logger.info("WebSocket连接已建立")
    
    try:
        while True:
            # 接收用户消息
            message = await websocket.receive_text()
            logger.info(f"收到用户消息: {message}")
            
            # 处理消息并流式返回结果
            try:
                async for response in cdm_agent.process_query(message):
                    await websocket.send_text(response)
            except Exception as e:
                error_msg = f"❌ 处理消息时出错: {str(e)}"
                logger.error(error_msg, exc_info=True)
                await websocket.send_text(error_msg)
                
    except WebSocketDisconnect:
        logger.info("WebSocket连接已断开")
    except Exception as e:
        logger.error(f"WebSocket异常: {e}", exc_info=True)


@app.get("/api/clusters")
async def get_clusters():
    """获取集群列表API"""
    try:
        clusters = await cdm_agent.get_available_clusters()
        return {"success": True, "data": clusters}
    except Exception as e:
        logger.error(f"获取集群列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/clusters/{cluster_id}")
async def get_cluster(cluster_id: str):
    """获取集群详情API"""
    try:
        cluster = await cdm_agent.get_cluster_info(cluster_id)
        if cluster:
            return {"success": True, "data": cluster}
        else:
            raise HTTPException(status_code=404, detail="集群未找到")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取集群详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/intents")
async def get_supported_intents():
    """获取支持的意图列表"""
    try:
        intents = cdm_agent.get_supported_intents()
        return {"success": True, "data": intents}
    except Exception as e:
        logger.error(f"获取意图列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/intents/{intent_name}/help")
async def get_intent_help(intent_name: str):
    """获取意图帮助信息"""
    try:
        help_text = cdm_agent.get_intent_help(intent_name)
        return {"success": True, "data": help_text}
    except Exception as e:
        logger.error(f"获取意图帮助失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/conversation")
async def get_conversation_history():
    """获取对话历史"""
    try:
        history = cdm_agent.get_conversation_history()
        return {"success": True, "data": history}
    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/conversation")
async def clear_conversation_history():
    """清空对话历史"""
    try:
        cdm_agent.clear_conversation_history()
        return {"success": True, "message": "对话历史已清空"}
    except Exception as e:
        logger.error(f"清空对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/health")
async def health_check():
    """系统健康检查"""
    try:
        health_status = await cdm_agent.health_check()
        return {"success": True, "data": health_status}
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def run_mcp_server():
    """运行MCP服务器"""
    if not MCP_AVAILABLE:
        logger.error("MCP服务器不可用，请检查MCP库安装")
        logger.info("可以使用以下命令安装MCP库:")
        logger.info("pip install mcp")
        return
        
    try:
        server = CDMMCPServer()
        await server.run()
    except ImportError as e:
        logger.error(f"MCP服务器启动失败: {e}")
        logger.info("请检查MCP库版本是否兼容")


async def run_web_server():
    """运行Web服务器"""
    uvicorn_config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
    server = uvicorn.Server(uvicorn_config)
    await server.serve()


async def main():
    """主函数"""
    logger.info("启动华为云CDM AI Agent...")
    
    # 检查运行模式
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == "mcp":
            logger.info("启动MCP服务器模式...")
            await run_mcp_server()
        elif mode == "web":
            logger.info("启动Web服务器模式...")
            await run_web_server()
        else:
            print("使用方法:")
            print("  python main.py mcp   # 启动MCP服务器")
            print("  python main.py web   # 启动Web服务器")
            sys.exit(1)
    else:
        # 默认启动Web服务器
        logger.info("启动Web服务器模式...")
        await run_web_server()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        sys.exit(1)

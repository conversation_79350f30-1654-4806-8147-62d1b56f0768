"""
基于LLM的CDM AI Agent核心模块
"""
import asyncio
import json
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime

from loguru import logger

from config import config
from cdm_api.client import CDMClient
from mcp_server.tools import CDMTools
from llm.client import LLMClient
from llm.function_calling import FunctionCallHandler
from llm.prompt_manager import PromptManager


class CDMAgent:
    """基于LLM的CDM AI Agent核心类"""
    
    def __init__(self, cdm_client: CDMClient = None):
        self.cdm_client = cdm_client or CDMClient()
        self.tools = CDMTools(self.cdm_client)
        
        # LLM相关组件
        self.llm_client = LLMClient()
        self.function_handler = FunctionCallHandler(self.tools)
        self.prompt_manager = PromptManager()
        
        # 对话状态管理
        self.conversation_history = []
        self.context = {}
        self.current_session_id = None
        
    async def process_query(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
        """基于LLM处理用户查询"""
        try:
            # 更新上下文
            if context:
                self.context.update(context)
            
            logger.info(f"处理用户查询: {query}")
            
            # 准备对话消息
            messages = self._prepare_messages(query)
            
            # 获取可用函数
            available_functions = self.function_handler.get_available_functions()
            
            # 调用LLM
            function_call_count = 0
            max_iterations = config.max_function_calls
            
            while function_call_count < max_iterations:
                logger.info(f"LLM调用轮次: {function_call_count + 1}")
                
                response = await self.llm_client.chat(
                    messages=messages,
                    functions=available_functions,
                    stream=False
                )
                
                # 处理LLM响应
                if response.get("tool_calls"):
                    # 有函数调用
                    function_call_count += 1
                    yield f"🔧 执行操作中... ({function_call_count}/{max_iterations})\n"
                    
                    # 解析并执行函数调用
                    function_calls = self.function_handler.parse_function_calls(response["tool_calls"])
                    
                    if function_calls:
                        # 执行函数调用
                        results = await self.function_handler.execute_function_calls(function_calls)
                        
                        # 将结果格式化为消息
                        result_messages = self.function_handler.format_function_results_for_llm(results)
                        
                        # 添加助手消息和函数结果到历史
                        messages.append(self.prompt_manager.create_assistant_message(
                            content=response.get("content", ""),
                            tool_calls=response.get("tool_calls")
                        ))
                        messages.extend(result_messages)
                        
                        # 输出中间结果
                        for result in results:
                            if result.success and result.result:
                                yield f"✅ {result.name}: 执行成功\n"
                            elif not result.success:
                                yield f"❌ {result.name}: {result.error}\n"
                        
                        continue  # 继续下一轮对话
                    else:
                        yield "❌ 函数调用解析失败\n"
                        break
                else:
                    # 没有函数调用，LLM给出最终回复
                    final_response = response.get("content", "")
                    if final_response:
                        yield f"💬 {final_response}\n"
                    else:
                        yield "✅ 操作完成\n"
                    break
            
            if function_call_count >= max_iterations:
                yield f"⚠️ 达到最大函数调用次数限制 ({max_iterations})，停止执行\n"
            
            # 更新对话历史
            self._update_conversation_history(query, messages)
            
        except Exception as e:
            error_msg = f"❌ 处理查询时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield error_msg
    
    def _prepare_messages(self, query: str) -> List[Dict[str, str]]:
        """准备对话消息"""
        messages = []
        
        # 添加系统消息
        messages.append(self.prompt_manager.create_system_message())
        
        # 添加历史对话（限制长度）
        if self.conversation_history:
            # 获取最近的对话历史
            recent_history = self.conversation_history[-config.conversation_memory:]
            for msg in recent_history:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
        
        # 添加当前用户消息
        user_message = self.prompt_manager.create_user_message(query, self.context)
        messages.append(user_message)
        
        # 截断消息以适应token限制
        token_limit = config.max_tokens * 0.6  # 留出输出空间
        messages = self.llm_client.truncate_messages(messages, int(token_limit))
        
        return messages
    
    def _update_conversation_history(self, query: str, messages: List[Dict[str, str]]):
        """更新对话历史"""
        # 添加用户消息
        self.conversation_history.append({
            "role": "user",
            "content": query,
            "timestamp": datetime.now()
        })
        
        # 添加最后一条助手消息
        for msg in reversed(messages):
            if msg["role"] == "assistant" and msg.get("content"):
                self.conversation_history.append({
                    "role": "assistant", 
                    "content": msg["content"],
                    "timestamp": datetime.now()
                })
                break
        
        # 限制历史长度
        max_history = config.conversation_memory * 2  # 用户+助手消息对
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
    
    # 流式对话接口
    async def stream_chat(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        async for response in self.process_query(query, context):
            yield response
    
    async def get_available_clusters(self) -> List[Dict[str, Any]]:
        """获取可用集群列表"""
        try:
            result = await self.tools.call_tool("cdm_list_clusters", {})
            if result and result[0].text:
                clusters_data = json.loads(result[0].text)
                return clusters_data.get("clusters", [])
        except Exception as e:
            logger.error(f"获取集群列表失败: {e}")
        return []
    
    async def get_cluster_info(self, cluster_id: str) -> Optional[Dict[str, Any]]:
        """获取集群信息"""
        try:
            result = await self.tools.call_tool("cdm_get_cluster", {"cluster_id": cluster_id})
            if result and result[0].text:
                return json.loads(result[0].text)
        except Exception as e:
            logger.error(f"获取集群信息失败: {e}")
        return None
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        
    def update_context(self, key: str, value: Any):
        """更新上下文"""
        self.context[key] = value
        
    def get_context(self) -> Dict[str, Any]:
        """获取当前上下文"""
        return self.context.copy()
    
    def get_supported_intents(self) -> List[str]:
        """获取支持的意图列表 (基于LLM，返回功能描述)"""
        return [
            "集群管理 - 创建、删除、启动、停止、重启集群",
            "连接管理 - 创建、更新、删除数据源连接",
            "作业管理 - 创建、启动、停止、监控数据迁移作业",
            "数据迁移 - 端到端的数据迁移流程",
            "系统查询 - 获取版本、规格等系统信息",
            "监控运维 - 实时监控集群和作业状态"
        ]
    
    def get_intent_help(self, intent_name: str = None) -> str:
        """获取帮助信息 (基于LLM增强)"""
        if intent_name:
            return self.prompt_manager.create_help_prompt(intent_name)
        else:
            return self.prompt_manager.create_help_prompt()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查LLM连接
            test_messages = [
                self.prompt_manager.create_system_message(),
                {"role": "user", "content": "健康检查"}
            ]
            
            response = await self.llm_client.chat(test_messages, stream=False)
            llm_status = "healthy" if response else "unhealthy"
            
            # 检查CDM API连接
            try:
                await self.tools.call_tool("cdm_get_cluster_versions", {})
                cdm_status = "healthy"
            except Exception:
                cdm_status = "unhealthy"
            
            return {
                "status": "healthy" if llm_status == "healthy" and cdm_status == "healthy" else "unhealthy",
                "llm_provider": config.llm_provider,
                "llm_status": llm_status,
                "cdm_status": cdm_status,
                "conversation_count": len(self.conversation_history),
                "context_keys": list(self.context.keys())
            }
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的工具函数
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cdm_api.tools import CDMTools
from loguru import logger


async def test_new_functions():
    """测试新增的工具函数"""
    print("=" * 60)
    print("测试新增的CDM工具函数")
    print("=" * 60)
    
    cdm_tools = CDMTools(mock_mode=True)
    cluster_id = "cluster-001"
    connection_name = "mysql-connection"
    job_name = "mysql2dws-job"
    
    # 测试集群管理功能
    print("\n1. 测试集群管理功能")
    print("-" * 30)
    
    # 测试重启集群
    try:
        result = await cdm_tools.execute_function("restart_cluster", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 重启集群: {result['message']}")
        else:
            print(f"[ERROR] 重启集群失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 重启集群异常: {e}")
    
    # 测试启动集群
    try:
        result = await cdm_tools.execute_function("start_cluster", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 启动集群: {result['message']}")
        else:
            print(f"[ERROR] 启动集群失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 启动集群异常: {e}")
    
    # 测试停止集群
    try:
        result = await cdm_tools.execute_function("stop_cluster", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 停止集群: {result['message']}")
        else:
            print(f"[ERROR] 停止集群失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 停止集群异常: {e}")
    
    # 测试删除集群（危险操作，注意）
    try:
        result = await cdm_tools.execute_function("delete_cluster", {"cluster_id": "test-cluster-delete"})
        if result.get("success"):
            print(f"[OK] 删除集群: {result['message']}")
        else:
            print(f"[ERROR] 删除集群失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 删除集群异常: {e}")
    
    # 测试连接管理功能
    print("\n2. 测试连接管理功能")
    print("-" * 30)
    
    # 测试获取连接详情
    try:
        result = await cdm_tools.execute_function("get_connection", {
            "cluster_id": cluster_id,
            "connection_name": connection_name
        })
        if result.get("success"):
            print(f"[OK] 获取连接详情: {result['message']}")
            conn_data = result['data']
            print(f"    连接名称: {conn_data['name']}")
            print(f"    连接器: {conn_data['connector_name']}")
        else:
            print(f"[ERROR] 获取连接详情失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 获取连接详情异常: {e}")
    
    # 测试删除连接
    try:
        result = await cdm_tools.execute_function("delete_connection", {
            "cluster_id": cluster_id,
            "connection_name": "test-connection-delete"
        })
        if result.get("success"):
            print(f"[OK] 删除连接: {result['message']}")
        else:
            print(f"[ERROR] 删除连接失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 删除连接异常: {e}")
    
    # 测试作业管理功能
    print("\n3. 测试作业管理功能")
    print("-" * 30)
    
    # 测试获取作业详情
    try:
        result = await cdm_tools.execute_function("get_job", {
            "cluster_id": cluster_id,
            "job_name": job_name
        })
        if result.get("success"):
            print(f"[OK] 获取作业详情: {result['message']}")
            job_data = result['data']
            print(f"    作业名称: {job_data['name']}")
            print(f"    作业类型: {job_data['type']}")
            print(f"    源连接: {job_data['from_link_name']}")
            print(f"    目标连接: {job_data['to_link_name']}")
        else:
            print(f"[ERROR] 获取作业详情失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 获取作业详情异常: {e}")
    
    # 测试获取作业执行历史
    try:
        result = await cdm_tools.execute_function("get_job_executions", {
            "cluster_id": cluster_id,
            "job_name": job_name
        })
        if result.get("success"):
            print(f"[OK] 获取作业执行历史: {result['message']}")
            executions = result['data']['executions']
            for i, exec in enumerate(executions[:2]):  # 只显示前2个
                print(f"    执行{i+1}: {exec['submission_id']} (状态: {exec['status']})")
        else:
            print(f"[ERROR] 获取作业执行历史失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 获取作业执行历史异常: {e}")
    
    # 测试停止作业
    try:
        result = await cdm_tools.execute_function("stop_job", {
            "cluster_id": cluster_id,
            "job_name": job_name
        })
        if result.get("success"):
            print(f"[OK] 停止作业: {result['message']}")
        else:
            print(f"[ERROR] 停止作业失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 停止作业异常: {e}")
    
    # 测试删除作业
    try:
        result = await cdm_tools.execute_function("delete_job", {
            "cluster_id": cluster_id,
            "job_name": "test-job-delete"
        })
        if result.get("success"):
            print(f"[OK] 删除作业: {result['message']}")
        else:
            print(f"[ERROR] 删除作业失败: {result.get('error')}")
    except Exception as e:
        print(f"[ERROR] 删除作业异常: {e}")
    
    print("\n" + "=" * 60)
    print("新增功能测试完成！")
    print("现在AI助手可以执行更多CDM操作，包括：")
    print("- 集群的启动、停止、重启、删除")
    print("- 连接的查看详情、删除")  
    print("- 作业的查看详情、停止、删除、查看执行历史")
    print("=" * 60)


async def main():
    """主函数"""
    print("测试新增的CDM工具函数...")
    print("注意: 请确保mock服务器正在运行")
    print()
    
    # 检查mock服务是否运行
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000", timeout=5)
            if response.status_code == 200:
                print("[OK] Mock服务器正在运行")
            else:
                print("[ERROR] Mock服务器响应异常")
                return
    except Exception as e:
        print(f"[ERROR] 无法连接到Mock服务器: {e}")
        print("请先启动mock服务器: python cdm_api\\mock_server.py")
        return
    
    await test_new_functions()


if __name__ == "__main__":
    asyncio.run(main())
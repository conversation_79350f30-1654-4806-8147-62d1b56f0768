"""
华为云认证模块
"""
import asyncio
import hashlib
import hmac
import json
import time
from datetime import datetime
from typing import Dict, Optional
from urllib.parse import quote

import httpx
from loguru import logger

from .exceptions import CDMAuthError, CDMAPIError


class MockAuth:
    """Mock认证类，用于测试"""
    
    def __init__(self, mock_mode: bool = True):
        self.mock_mode = mock_mode
        self._fake_token = "mock-token-12345678"
        
    async def get_token(self) -> str:
        """返回假token"""
        logger.info("Using mock token for testing")
        return self._fake_token
        
    def get_auth_headers(self, token: str) -> Dict[str, str]:
        """获取认证头"""
        return {
            "X-Auth-Token": token,
            "Content-Type": "application/json"
        }


class HuaweiCloudAuth:
    """华为云认证类"""
    
    def __init__(self, username: str, password: str, domain_name: str, project_id: str, 
                 iam_endpoint: str):
        self.username = username
        self.password = password
        self.domain_name = domain_name
        self.project_id = project_id
        self.iam_endpoint = iam_endpoint
        self._token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
        
    async def get_token(self) -> str:
        """获取访问令牌"""
        if self._token and self._token_expires_at and datetime.now() < self._token_expires_at:
            return self._token
            
        await self._refresh_token()
        return self._token
        
    async def _refresh_token(self):
        """刷新访问令牌"""
        auth_data = {
            "auth": {
                "identity": {
                    "methods": ["password"],
                    "password": {
                        "user": {
                            "name": self.username,
                            "password": self.password,
                            "domain": {
                                "name": self.domain_name
                            }
                        }
                    }
                },
                "scope": {
                    "project": {
                        "id": self.project_id
                    }
                }
            }
        }
        
        url = f"{self.iam_endpoint}/v3/auth/tokens"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=auth_data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                
            if response.status_code == 201:
                self._token = response.headers.get("X-Subject-Token")
                
                token_data = response.json()
                expires_at_str = token_data["token"]["expires_at"]
                self._token_expires_at = datetime.fromisoformat(
                    expires_at_str.replace('Z', '+00:00')
                )
                
                logger.info("Successfully obtained authentication token")
            else:
                error_msg = f"Authentication failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise CDMAuthError(error_msg)
                
        except httpx.TimeoutException:
            raise CDMAuthError("Authentication request timeout")
        except Exception as e:
            raise CDMAuthError(f"Authentication error: {str(e)}")
            
    def get_auth_headers(self, token: str) -> Dict[str, str]:
        """获取认证头"""
        return {
            "X-Auth-Token": token,
            "Content-Type": "application/json"
        }


class HuaweiCloudSigner:
    """华为云API签名器（用于AK/SK认证）"""
    
    def __init__(self, access_key: str, secret_key: str):
        self.access_key = access_key
        self.secret_key = secret_key
        
    def sign_request(self, method: str, url: str, headers: Dict[str, str], 
                    body: str = None) -> Dict[str, str]:
        """对请求进行签名"""
        from urllib.parse import urlparse, unquote
        
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = unquote(parsed_url.path) if parsed_url.path else "/"
        query = parsed_url.query
        
        # 构建规范化请求
        timestamp = datetime.utcnow().strftime("%Y%m%dT%H%M%SZ")
        date = timestamp[:8]
        
        # 构建授权头
        algorithm = "AWS4-HMAC-SHA256"
        credential_scope = f"{date}/cn-north-1/cdm/aws4_request"
        credential = f"{self.access_key}/{credential_scope}"
        
        # 添加必要头部
        headers = headers.copy()
        headers["Host"] = host
        headers["X-Amz-Date"] = timestamp
        
        if body:
            headers["X-Amz-Content-Sha256"] = hashlib.sha256(body.encode()).hexdigest()
        else:
            headers["X-Amz-Content-Sha256"] = hashlib.sha256(b"").hexdigest()
            
        # 构建规范化头部
        signed_headers = ";".join(sorted(headers.keys(), key=str.lower))
        canonical_headers = ""
        for key in sorted(headers.keys(), key=str.lower):
            canonical_headers += f"{key.lower()}:{headers[key]}\n"
            
        # 构建规范化请求字符串
        canonical_request = "\n".join([
            method.upper(),
            path,
            query,
            canonical_headers,
            signed_headers,
            headers["X-Amz-Content-Sha256"]
        ])
        
        # 构建待签名字符串
        canonical_request_hash = hashlib.sha256(canonical_request.encode()).hexdigest()
        string_to_sign = "\n".join([
            algorithm,
            timestamp,
            credential_scope,
            canonical_request_hash
        ])
        
        # 计算签名
        signing_key = self._get_signing_key(date)
        signature = hmac.new(signing_key, string_to_sign.encode(), hashlib.sha256).hexdigest()
        
        # 构建授权头
        authorization = (
            f"{algorithm} Credential={credential}, "
            f"SignedHeaders={signed_headers}, Signature={signature}"
        )
        
        headers["Authorization"] = authorization
        return headers
        
    def _get_signing_key(self, date: str) -> bytes:
        """获取签名密钥"""
        def _sign(key: bytes, msg: str) -> bytes:
            return hmac.new(key, msg.encode(), hashlib.sha256).digest()
            
        k_date = _sign(f"AWS4{self.secret_key}".encode(), date)
        k_region = _sign(k_date, "cn-north-1")
        k_service = _sign(k_region, "cdm")
        k_signing = _sign(k_service, "aws4_request")
        
        return k_signing
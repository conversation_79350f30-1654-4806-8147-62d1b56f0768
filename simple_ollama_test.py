# -*- coding: utf-8 -*-
"""
简化的Ollama CDM Agent测试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm.client import LLMClient
from config import config

async def test_ollama_connection():
    """测试Ollama连接"""
    print("Testing Ollama connection...")
    
    try:
        llm_client = LLMClient()
        
        messages = [
            {"role": "system", "content": "你是一个AI助手，请用中文回答问题。"},
            {"role": "user", "content": "你好，请介绍一下你自己。"}
        ]
        
        print("Sending request to Ollama...")
        response = await llm_client.chat(messages)
        
        print(f"Response: {response}")
        
        if response.get('content'):
            print("Ollama integration test PASSED!")
            return True
        else:
            print("Ollama integration test FAILED - No content in response")
            return False
            
    except Exception as e:
        print(f"Ollama integration test FAILED - Error: {e}")
        return False

async def test_function_calling():
    """测试函数调用能力"""
    print("\nTesting function calling...")
    
    try:
        llm_client = LLMClient()
        
        # 简单的函数定义
        functions = [{
            "name": "get_weather",
            "description": "获取指定城市的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        }]
        
        messages = [
            {"role": "system", "content": "你是一个AI助手，当用户询问天气时，请使用get_weather函数。"},
            {"role": "user", "content": "北京今天天气怎么样？"}
        ]
        
        print("Sending function call request to Ollama...")
        response = await llm_client.chat(messages, functions=functions)
        
        print(f"Function call response: {response}")
        
        if 'tool_calls' in response:
            print("Function calling test PASSED!")
            return True
        else:
            print("Function calling test - No function calls detected")
            return False
            
    except Exception as e:
        print(f"Function calling test FAILED - Error: {e}")
        return False

async def main():
    """主测试函数"""
    print("Starting Ollama CDM Agent Integration Test")
    print(f"Using model: {config.ollama_model}")
    print(f"Ollama URL: {config.ollama_base_url}")
    print("-" * 50)
    
    # 测试基本连接
    basic_ok = await test_ollama_connection()
    
    # 测试函数调用
    function_ok = await test_function_calling()
    
    print("-" * 50)
    print("Test Summary:")
    print(f"Basic Connection: {'PASSED' if basic_ok else 'FAILED'}")
    print(f"Function Calling: {'PASSED' if function_ok else 'FAILED'}")
    
    if basic_ok:
        print("\nOllama integration is working! You can now test the full CDM Agent.")
        return True
    else:
        print("\nOllama integration failed. Please check your configuration.")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
2025-09-09 21:20:30.527 | INFO     | __main__:main:268 - 检查Mock服务是否运行...
2025-09-09 21:20:30.837 | INFO     | __main__:main:272 - Mock服务状态: 200
2025-09-09 21:20:30.837 | INFO     | __main__:main:273 - 服务信息: {
  "message": "华为云CDM API Mock服务",
  "version": "1.0.0",
  "docs": "/docs"
}
2025-09-09 21:20:30.837 | INFO     | __main__:run_all_tests:250 - 开始运行CDM Mock服务测试...
2025-09-09 21:20:30.837 | INFO     | __main__:test_cluster_apis:23 - 开始测试集群相关API...
2025-09-09 21:20:30.840 | INFO     | __main__:test_cluster_apis:27 - 测试查询集群列表
2025-09-09 21:20:31.105 | INFO     | __main__:test_cluster_apis:29 - 集群列表响应: 200
2025-09-09 21:20:31.105 | INFO     | __main__:test_cluster_apis:30 - 响应内容: {
  "clusters": [
    {
      "id": "cluster-001",
      "name": "cdm-cluster-01",
      "status": "200",
      "statusDetail": "Normal",
      "created": "2025-09-09T19:20:31.101996Z",
      "updated": "2025-09-09T21:20:31.101996Z",
      "mode": "sharding",
      "flavor": {
        "id": "cdm.medium",
        "name": "CDM Medium",
        "cpu": "4",
        "memory": "16",
        "disk": "100"
      },
      "datastore": {
        "type": "cdm",
        "version": "2.9.2.200"
      },
      "instances": [
        {
          "id": "instance-0",
          "name": "cdm-cluster-01-0",
          "role": "master",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-1",
          "name": "cdm-cluster-01-1",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-2",
          "name": "cdm-cluster-01-2",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        }
      ],
      "publicEndpoint": "cdm-cluster-.example.com",
      "endpoint": "192.168.1.101",
      "securityGroupId": "sg-12345678",
      "subnetId": "subnet-12345678",
      "vpcId": "vpc-12345678",
      "availabilityZone": "cn-north-1a",
      "enterpriseProjectId": "0"
    },
    {
      "id": "cluster-002",
      "name": "cdm-cluster-01",
      "status": "200",
      "statusDetail": "Normal",
      "created": "2025-09-09T19:20:31.101996Z",
      "updated": "2025-09-09T21:20:31.101996Z",
      "mode": "sharding",
      "flavor": {
        "id": "cdm.medium",
        "name": "CDM Medium",
        "cpu": "4",
        "memory": "16",
        "disk": "100"
      },
      "datastore": {
        "type": "cdm",
        "version": "2.9.2.200"
      },
      "instances": [
        {
          "id": "instance-0",
          "name": "cdm-cluster-01-0",
          "role": "master",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-1",
          "name": "cdm-cluster-01-1",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-2",
          "name": "cdm-cluster-01-2",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        }
      ],
      "publicEndpoint": "cdm-cluster-.example.com",
      "endpoint": "192.168.1.102",
      "securityGroupId": "sg-12345678",
      "subnetId": "subnet-12345678",
      "vpcId": "vpc-12345678",
      "availabilityZone": "cn-north-1a",
      "enterpriseProjectId": "0"
    },
    {
      "id": "cluster-003",
      "name": "cdm-cluster-01",
      "status": "200",
      "statusDetail": "Normal",
      "created": "2025-09-09T19:20:31.101996Z",
      "updated": "2025-09-09T21:20:31.101996Z",
      "mode": "sharding",
      "flavor": {
        "id": "cdm.medium",
        "name": "CDM Medium",
        "cpu": "4",
        "memory": "16",
        "disk": "100"
      },
      "datastore": {
        "type": "cdm",
        "version": "2.9.2.200"
      },
      "instances": [
        {
          "id": "instance-0",
          "name": "cdm-cluster-01-0",
          "role": "master",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-1",
          "name": "cdm-cluster-01-1",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        },
        {
          "id": "instance-2",
          "name": "cdm-cluster-01-2",
          "role": "worker",
          "status": "200",
          "flavor": "cdm.medium"
        }
      ],
      "publicEndpoint": "cdm-cluster-.example.com",
      "endpoint": "192.168.1.103",
      "securityGroupId": "sg-12345678",
      "subnetId": "subnet-12345678",
      "vpcId": "vpc-12345678",
      "availabilityZone": "cn-north-1a",
      "enterpriseProjectId": "0"
    }
  ],
  "total": 3
}
2025-09-09 21:20:31.105 | INFO     | __main__:test_cluster_apis:33 - 测试查询集群详情
2025-09-09 21:20:31.110 | INFO     | __main__:test_cluster_apis:35 - 集群详情响应: 200
2025-09-09 21:20:31.110 | INFO     | __main__:test_cluster_apis:36 - 响应内容: {
  "cluster": {
    "id": "test-cluster-id",
    "name": "cdm-cluster-01",
    "status": "200",
    "statusDetail": "Normal",
    "created": "2025-09-09T19:20:31.108900Z",
    "updated": "2025-09-09T21:20:31.108900Z",
    "mode": "sharding",
    "flavor": {
      "id": "cdm.medium",
      "name": "CDM Medium",
      "cpu": "4",
      "memory": "16",
      "disk": "100"
    },
    "datastore": {
      "type": "cdm",
      "version": "2.9.2.200"
    },
    "instances": [
      {
        "id": "instance-0",
        "name": "cdm-cluster-01-0",
        "role": "master",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-1",
        "name": "cdm-cluster-01-1",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-2",
        "name": "cdm-cluster-01-2",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      }
    ],
    "publicEndpoint": "cdm-test-clu.example.com",
    "endpoint": "192.168.1.10d",
    "securityGroupId": "sg-12345678",
    "subnetId": "subnet-12345678",
    "vpcId": "vpc-12345678",
    "availabilityZone": "cn-north-1a",
    "enterpriseProjectId": "0"
  }
}
2025-09-09 21:20:31.110 | INFO     | __main__:test_cluster_apis:39 - 测试创建集群
2025-09-09 21:20:31.112 | INFO     | __main__:test_cluster_apis:62 - 创建集群响应: 200
2025-09-09 21:20:31.112 | INFO     | __main__:test_cluster_apis:63 - 响应内容: {
  "id": "279c98c6-179c-42f5-9a13-ff2dbf0ff0e0"
}
2025-09-09 21:20:31.112 | INFO     | __main__:test_cluster_apis:66 - 测试集群重启操作
2025-09-09 21:20:31.114 | INFO     | __main__:test_cluster_apis:78 - 集群操作响应: 200
2025-09-09 21:20:31.114 | INFO     | __main__:test_cluster_apis:79 - 响应内容: {
  "jobId": "job-e75e7bf4",
  "message": "Cluster restart operation initiated"
}
2025-09-09 21:20:31.114 | INFO     | __main__:test_connection_apis:83 - 开始测试连接相关API...
2025-09-09 21:20:31.119 | INFO     | __main__:test_connection_apis:87 - 测试查询连接列表
2025-09-09 21:20:31.389 | INFO     | __main__:test_connection_apis:89 - 连接列表响应: 200
2025-09-09 21:20:31.389 | INFO     | __main__:test_connection_apis:90 - 响应内容: {
  "links": [
    {
      "name": "mysql-connection",
      "connector-name": "generic-jdbc-connector",
      "link-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "linkConfig.databaseType",
                "value": "MYSQL"
              },
              {
                "name": "linkConfig.host",
                "value": "*************"
              },
              {
                "name": "linkConfig.port",
                "value": "3306"
              },
              {
                "name": "linkConfig.database",
                "value": "testdb"
              },
              {
                "name": "linkConfig.username",
                "value": "root"
              },
              {
                "name": "linkConfig.password",
                "value": "******"
              }
            ],
            "name": "linkConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:20:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    },
    {
      "name": "dws-connection",
      "connector-name": "generic-jdbc-connector",
      "link-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "linkConfig.databaseType",
                "value": "MYSQL"
              },
              {
                "name": "linkConfig.host",
                "value": "*************"
              },
              {
                "name": "linkConfig.port",
                "value": "3306"
              },
              {
                "name": "linkConfig.database",
                "value": "testdb"
              },
              {
                "name": "linkConfig.username",
                "value": "root"
              },
              {
                "name": "linkConfig.password",
                "value": "******"
              }
            ],
            "name": "linkConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:20:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    },
    {
      "name": "obs-connection",
      "connector-name": "generic-jdbc-connector",
      "link-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "linkConfig.databaseType",
                "value": "MYSQL"
              },
              {
                "name": "linkConfig.host",
                "value": "*************"
              },
              {
                "name": "linkConfig.port",
                "value": "3306"
              },
              {
                "name": "linkConfig.database",
                "value": "testdb"
              },
              {
                "name": "linkConfig.username",
                "value": "root"
              },
              {
                "name": "linkConfig.password",
                "value": "******"
              }
            ],
            "name": "linkConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:20:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    }
  ]
}
2025-09-09 21:20:31.390 | INFO     | __main__:test_connection_apis:94 - 测试查询连接详情
2025-09-09 21:20:31.391 | INFO     | __main__:test_connection_apis:96 - 连接详情响应: 200
2025-09-09 21:20:31.391 | INFO     | __main__:test_connection_apis:97 - 响应内容: {
  "link": {
    "name": "mysql-connection",
    "connector-name": "generic-jdbc-connector",
    "link-config-values": {
      "configs": [
        {
          "inputs": [
            {
              "name": "linkConfig.databaseType",
              "value": "MYSQL"
            },
            {
              "name": "linkConfig.host",
              "value": "*************"
            },
            {
              "name": "linkConfig.port",
              "value": "3306"
            },
            {
              "name": "linkConfig.database",
              "value": "testdb"
            },
            {
              "name": "linkConfig.username",
              "value": "root"
            },
            {
              "name": "linkConfig.password",
              "value": "******"
            }
          ],
          "name": "linkConfig"
        }
      ]
    },
    "creation-user": "admin",
    "creation-date": "2025-09-09 20:20:31",
    "update-user": "admin",
    "update-date": "2025-09-09 21:20:31",
    "enabled": true
  }
}
2025-09-09 21:20:31.391 | INFO     | __main__:test_connection_apis:100 - 测试创建连接
2025-09-09 21:20:31.391 | INFO     | __main__:test_connection_apis:128 - 创建连接响应: 200
2025-09-09 21:20:31.391 | INFO     | __main__:test_connection_apis:129 - 响应内容: {
  "message": "Created 1 connections successfully",
  "connections": [
    "test-mysql-connection"
  ]
}
2025-09-09 21:20:31.391 | INFO     | __main__:test_job_apis:133 - 开始测试作业相关API...
2025-09-09 21:20:31.400 | INFO     | __main__:test_job_apis:137 - 测试查询作业列表
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:139 - 作业列表响应: 200
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:140 - 响应内容: {
  "jobs": [
    {
      "name": "mysql2dws-job",
      "type": "NORMAL_JOB",
      "from-connector-name": "generic-jdbc-connector",
      "to-connector-name": "generic-jdbc-connector",
      "from-link-name": "mysql-connection",
      "to-link-name": "dws-connection",
      "from-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "fromJobConfig.useSql",
                "value": "false"
              },
              {
                "name": "fromJobConfig.schemaName",
                "value": "testdb"
              },
              {
                "name": "fromJobConfig.tableName",
                "value": "users"
              },
              {
                "name": "fromJobConfig.columnList",
                "value": "id&name&email&created_at"
              },
              {
                "name": "fromJobConfig.incrMigration",
                "value": "false"
              }
            ],
            "name": "fromJobConfig"
          }
        ]
      },
      "to-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "toJobConfig.schemaName",
                "value": "public"
              },
              {
                "name": "toJobConfig.tablePreparation",
                "value": "DROP_AND_CREATE"
              },
              {
                "name": "toJobConfig.tableName",
                "value": "users_dws"
              }
            ],
            "name": "toJobConfig"
          }
        ]
      },
      "driver-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "driverConfig.numExtractors",
                "value": "1"
              },
              {
                "name": "driverConfig.numLoaders",
                "value": "1"
              }
            ],
            "name": "driverConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:50:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    },
    {
      "name": "obs2mysql-job",
      "type": "NORMAL_JOB",
      "from-connector-name": "generic-jdbc-connector",
      "to-connector-name": "generic-jdbc-connector",
      "from-link-name": "mysql-connection",
      "to-link-name": "dws-connection",
      "from-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "fromJobConfig.useSql",
                "value": "false"
              },
              {
                "name": "fromJobConfig.schemaName",
                "value": "testdb"
              },
              {
                "name": "fromJobConfig.tableName",
                "value": "users"
              },
              {
                "name": "fromJobConfig.columnList",
                "value": "id&name&email&created_at"
              },
              {
                "name": "fromJobConfig.incrMigration",
                "value": "false"
              }
            ],
            "name": "fromJobConfig"
          }
        ]
      },
      "to-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "toJobConfig.schemaName",
                "value": "public"
              },
              {
                "name": "toJobConfig.tablePreparation",
                "value": "DROP_AND_CREATE"
              },
              {
                "name": "toJobConfig.tableName",
                "value": "users_dws"
              }
            ],
            "name": "toJobConfig"
          }
        ]
      },
      "driver-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "driverConfig.numExtractors",
                "value": "1"
              },
              {
                "name": "driverConfig.numLoaders",
                "value": "1"
              }
            ],
            "name": "driverConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:50:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    },
    {
      "name": "hive2dws-job",
      "type": "NORMAL_JOB",
      "from-connector-name": "generic-jdbc-connector",
      "to-connector-name": "generic-jdbc-connector",
      "from-link-name": "mysql-connection",
      "to-link-name": "dws-connection",
      "from-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "fromJobConfig.useSql",
                "value": "false"
              },
              {
                "name": "fromJobConfig.schemaName",
                "value": "testdb"
              },
              {
                "name": "fromJobConfig.tableName",
                "value": "users"
              },
              {
                "name": "fromJobConfig.columnList",
                "value": "id&name&email&created_at"
              },
              {
                "name": "fromJobConfig.incrMigration",
                "value": "false"
              }
            ],
            "name": "fromJobConfig"
          }
        ]
      },
      "to-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "toJobConfig.schemaName",
                "value": "public"
              },
              {
                "name": "toJobConfig.tablePreparation",
                "value": "DROP_AND_CREATE"
              },
              {
                "name": "toJobConfig.tableName",
                "value": "users_dws"
              }
            ],
            "name": "toJobConfig"
          }
        ]
      },
      "driver-config-values": {
        "configs": [
          {
            "inputs": [
              {
                "name": "driverConfig.numExtractors",
                "value": "1"
              },
              {
                "name": "driverConfig.numLoaders",
                "value": "1"
              }
            ],
            "name": "driverConfig"
          }
        ]
      },
      "creation-user": "admin",
      "creation-date": "2025-09-09 20:50:31",
      "update-user": "admin",
      "update-date": "2025-09-09 21:20:31",
      "enabled": true
    }
  ]
}
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:144 - 测试查询作业详情
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:146 - 作业详情响应: 200
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:147 - 响应内容: {
  "job": {
    "name": "mysql2dws-job",
    "type": "NORMAL_JOB",
    "from-connector-name": "generic-jdbc-connector",
    "to-connector-name": "generic-jdbc-connector",
    "from-link-name": "mysql-connection",
    "to-link-name": "dws-connection",
    "from-config-values": {
      "configs": [
        {
          "inputs": [
            {
              "name": "fromJobConfig.useSql",
              "value": "false"
            },
            {
              "name": "fromJobConfig.schemaName",
              "value": "testdb"
            },
            {
              "name": "fromJobConfig.tableName",
              "value": "users"
            },
            {
              "name": "fromJobConfig.columnList",
              "value": "id&name&email&created_at"
            },
            {
              "name": "fromJobConfig.incrMigration",
              "value": "false"
            }
          ],
          "name": "fromJobConfig"
        }
      ]
    },
    "to-config-values": {
      "configs": [
        {
          "inputs": [
            {
              "name": "toJobConfig.schemaName",
              "value": "public"
            },
            {
              "name": "toJobConfig.tablePreparation",
              "value": "DROP_AND_CREATE"
            },
            {
              "name": "toJobConfig.tableName",
              "value": "users_dws"
            }
          ],
          "name": "toJobConfig"
        }
      ]
    },
    "driver-config-values": {
      "configs": [
        {
          "inputs": [
            {
              "name": "driverConfig.numExtractors",
              "value": "1"
            },
            {
              "name": "driverConfig.numLoaders",
              "value": "1"
            }
          ],
          "name": "driverConfig"
        }
      ]
    },
    "creation-user": "admin",
    "creation-date": "2025-09-09 20:50:31",
    "update-user": "admin",
    "update-date": "2025-09-09 21:20:31",
    "enabled": true
  }
}
2025-09-09 21:20:31.652 | INFO     | __main__:test_job_apis:150 - 测试创建作业
2025-09-09 21:20:31.658 | INFO     | __main__:test_job_apis:193 - 创建作业响应: 200
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:194 - 响应内容: {
  "message": "Created 1 jobs successfully",
  "jobs": [
    "test-mysql2dws-job"
  ]
}
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:197 - 测试启动作业
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:201 - 启动作业响应: 200
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:202 - 响应内容: {
  "submission-id": "df8eb783-0e5b-456b-985c-d24988143cee"
}
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:205 - 测试查询作业状态
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:209 - 作业状态响应: 200
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:210 - 响应内容: {
  "status": "RUNNING",
  "progress": 0.0,
  "message": "Job mysql2dws-job is running"
}
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:213 - 测试查询作业执行历史
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:217 - 作业执行历史响应: 200
2025-09-09 21:20:31.659 | INFO     | __main__:test_job_apis:218 - 响应内容: {
  "submissions": [
    {
      "submission-id": "df8eb783-0e5b-456b-985c-d24988143cee",
      "job-name": "mysql2dws-job",
      "status": "RUNNING",
      "creation-date": "2025-09-09 21:10:31",
      "last-update-date": "2025-09-09 21:15:31",
      "counters": {
        "org.apache.sqoop.submission.counter.SqloopCounters": {
          "BYTES_READ": 1024000,
          "TOTAL_RECORDS_READ": 1000,
          "BYTES_WRITTEN": 1024000,
          "TOTAL_RECORDS_WRITTEN": 1000
        }
      },
      "progress": 100.0
    }
  ]
}
2025-09-09 21:20:31.659 | INFO     | __main__:test_auxiliary_apis:222 - 开始测试辅助API...
2025-09-09 21:20:31.669 | INFO     | __main__:test_auxiliary_apis:226 - 测试获取集群版本列表
2025-09-09 21:20:31.921 | INFO     | __main__:test_auxiliary_apis:228 - 版本列表响应: 200
2025-09-09 21:20:31.921 | INFO     | __main__:test_auxiliary_apis:229 - 响应内容: {
  "cluster": {
    "id": "versions",
    "name": "cdm-cluster-01",
    "status": "200",
    "statusDetail": "Normal",
    "created": "2025-09-09T19:20:31.921177Z",
    "updated": "2025-09-09T21:20:31.921177Z",
    "mode": "sharding",
    "flavor": {
      "id": "cdm.medium",
      "name": "CDM Medium",
      "cpu": "4",
      "memory": "16",
      "disk": "100"
    },
    "datastore": {
      "type": "cdm",
      "version": "2.9.2.200"
    },
    "instances": [
      {
        "id": "instance-0",
        "name": "cdm-cluster-01-0",
        "role": "master",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-1",
        "name": "cdm-cluster-01-1",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-2",
        "name": "cdm-cluster-01-2",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      }
    ],
    "publicEndpoint": "cdm-versions.example.com",
    "endpoint": "192.168.1.10s",
    "securityGroupId": "sg-12345678",
    "subnetId": "subnet-12345678",
    "vpcId": "vpc-12345678",
    "availabilityZone": "cn-north-1a",
    "enterpriseProjectId": "0"
  }
}
2025-09-09 21:20:31.921 | INFO     | __main__:test_auxiliary_apis:232 - 测试获取集群规格列表
2025-09-09 21:20:31.925 | INFO     | __main__:test_auxiliary_apis:234 - 规格列表响应: 200
2025-09-09 21:20:31.925 | INFO     | __main__:test_auxiliary_apis:235 - 响应内容: {
  "cluster": {
    "id": "flavors",
    "name": "cdm-cluster-01",
    "status": "200",
    "statusDetail": "Normal",
    "created": "2025-09-09T19:20:31.921177Z",
    "updated": "2025-09-09T21:20:31.921177Z",
    "mode": "sharding",
    "flavor": {
      "id": "cdm.medium",
      "name": "CDM Medium",
      "cpu": "4",
      "memory": "16",
      "disk": "100"
    },
    "datastore": {
      "type": "cdm",
      "version": "2.9.2.200"
    },
    "instances": [
      {
        "id": "instance-0",
        "name": "cdm-cluster-01-0",
        "role": "master",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-1",
        "name": "cdm-cluster-01-1",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      },
      {
        "id": "instance-2",
        "name": "cdm-cluster-01-2",
        "role": "worker",
        "status": "200",
        "flavor": "cdm.medium"
      }
    ],
    "publicEndpoint": "cdm-flavors.example.com",
    "endpoint": "192.168.1.10s",
    "securityGroupId": "sg-12345678",
    "subnetId": "subnet-12345678",
    "vpcId": "vpc-12345678",
    "availabilityZone": "cn-north-1a",
    "enterpriseProjectId": "0"
  }
}
2025-09-09 21:20:31.925 | INFO     | __main__:test_error_handling:239 - 开始测试错误处理...
2025-09-09 21:20:31.931 | INFO     | __main__:test_error_handling:243 - 测试404错误
2025-09-09 21:20:32.184 | INFO     | __main__:test_error_handling:245 - 404错误响应: 404
2025-09-09 21:20:32.184 | INFO     | __main__:test_error_handling:246 - 响应内容: {
  "error_code": "CDM.0001",
  "error_msg": "Endpoint not found: /nonexistent/endpoint",
  "request_id": "424ce9ed-6f9a-4e09-ac69-9f3533f62197"
}
2025-09-09 21:20:32.184 | INFO     | __main__:run_all_tests:258 - 所有测试完成!

# -*- coding: utf-8 -*-
"""
CDM AI Agent 交互式测试
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm.client import LLMClient
from cdm_api.tools import CDMTools

class CDMAgent:
    def __init__(self):
        self.llm_client = LLMClient()
        self.cdm_tools = CDMTools(mock_mode=True)
        self.conversation_history = []
        
        self.system_prompt = """你是华为云CDM（云数据迁移）服务的智能助手。你可以帮助用户管理CDM集群、连接和数据迁移作业。

你的主要功能包括：
1. 查询和管理CDM集群
2. 创建和管理数据库连接
3. 创建和执行数据迁移作业
4. 监控作业状态

当用户提出CDM相关请求时，你应该：
1. 理解用户的需求
2. 选择合适的工具函数来完成任务
3. 清晰地解释操作结果
4. 如果遇到问题，提供解决建议

请用简洁、专业的中文回答用户问题。"""

    async def chat(self, user_input):
        """与用户对话"""
        print("正在思考...")
        
        # 构建消息
        messages = [{"role": "system", "content": self.system_prompt}]
        
        # 添加历史对话（最近10轮）
        for msg in self.conversation_history[-20:]:
            messages.append(msg)
        
        messages.append({"role": "user", "content": user_input})
        
        # 获取工具函数
        functions = self.cdm_tools.get_function_definitions()
        
        try:
            # 调用LLM
            response = await self.llm_client.chat(messages, functions=functions)
            assistant_message = response.get("content", "")
            
            # 处理工具调用
            if "tool_calls" in response:
                import json
                tool_calls = response["tool_calls"]
                
                print("正在执行相关操作...")
                function_results = []
                
                for tool_call in tool_calls:
                    func_name = tool_call["function"]["name"]
                    func_args = json.loads(tool_call["function"]["arguments"])
                    
                    print(f"执行: {func_name}")
                    result = await self.cdm_tools.execute_function(func_name, func_args)
                    function_results.append(result)
                
                # 生成基于结果的回复
                if function_results:
                    results_summary = []
                    for i, result in enumerate(function_results):
                        func_name = tool_calls[i]["function"]["name"]
                        if result.get("success"):
                            results_summary.append(f"操作 {func_name} 执行成功")
                        else:
                            error_msg = result.get('error', '执行失败')
                            results_summary.append(f"操作 {func_name} 失败: {error_msg}")
                    
                    # 再次调用LLM生成最终回复
                    final_messages = messages + [
                        {"role": "assistant", "content": "我正在为您执行相关操作..."},
                        {"role": "user", "content": f"操作结果: {'; '.join(results_summary)}。请基于这些结果回复用户。"}
                    ]
                    
                    final_response = await self.llm_client.chat(final_messages)
                    assistant_message = final_response.get("content", "操作已完成。")
            
            # 保存对话历史
            self.conversation_history.append({"role": "user", "content": user_input})
            self.conversation_history.append({"role": "assistant", "content": assistant_message})
            
            return assistant_message
            
        except Exception as e:
            return f"抱歉，处理您的请求时遇到了问题: {str(e)}"

    def reset_conversation(self):
        """重置对话"""
        self.conversation_history = []
        print("对话历史已清除")

async def main():
    """交互式主程序"""
    print("CDM 智能助手已启动！")
    print("我可以帮您管理华为云CDM服务，包括集群、连接和数据迁移作业")
    print("输入 'quit' 或 'exit' 退出，输入 'reset' 清除对话历史")
    print("-" * 50)
    
    agent = CDMAgent()
    
    while True:
        try:
            user_input = input("\n您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("\n再见！")
                break
            elif user_input.lower() in ['reset', '重置']:
                agent.reset_conversation()
                continue
            elif not user_input:
                continue
            
            response = await agent.chat(user_input)
            print(f"\n助手: {response}")
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"\n错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
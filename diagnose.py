# -*- coding: utf-8 -*-
"""
诊断脚本 - 检查可能导致死循环的问题
"""
import asyncio
import httpx
import time
import signal
import sys

class ServiceDiagnostics:
    def __init__(self):
        self.timeout = 5.0  # 5秒超时
        
    async def check_ollama(self):
        """检查Ollama服务"""
        print("[INFO] 检查Ollama服务...")
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                start_time = time.time()
                response = await client.get("http://localhost:11434/api/tags")
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    qwen_models = [m["name"] for m in models if "qwen" in m["name"].lower()]
                    print(f"[OK] Ollama服务正常 (响应时间: {duration:.2f}s)")
                    if qwen_models:
                        print(f"     - 找到Qwen模型: {qwen_models}")
                    else:
                        print("     [WARN] 未找到Qwen模型")
                    return True
                else:
                    print(f"[ERROR] Ollama服务响应异常: {response.status_code}")
                    return False
        except asyncio.TimeoutError:
            print("[ERROR] Ollama服务超时 - 可能导致死循环")
            return False
        except Exception as e:
            print(f"[ERROR] Ollama服务错误: {e}")
            return False
    
    async def check_mock_service(self):
        """检查Mock服务"""
        print("[INFO] 检查CDM Mock服务...")
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                start_time = time.time()
                response = await client.get("http://localhost:8000/")
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    print(f"[OK] Mock服务正常 (响应时间: {duration:.2f}s)")
                    return True
                else:
                    print(f"[ERROR] Mock服务响应异常: {response.status_code}")
                    return False
        except asyncio.TimeoutError:
            print("[ERROR] Mock服务超时 - 可能导致死循环")
            return False
        except Exception as e:
            print(f"[ERROR] Mock服务错误: {e}")
            return False
    
    async def test_llm_integration(self):
        """测试LLM集成"""
        print("[INFO] 测试LLM集成...")
        try:
            # 简单的API调用测试
            async with httpx.AsyncClient(timeout=10.0) as client:
                start_time = time.time()
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": "qwen3:8b",
                        "prompt": "Hello, respond with just 'OK'",
                        "stream": False
                    }
                )
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    content = result.get("response", "").strip()
                    print(f"[OK] LLM响应正常 (响应时间: {duration:.2f}s)")
                    print(f"     - 响应内容: {content[:50]}...")
                    return True
                else:
                    print(f"[ERROR] LLM响应异常: {response.status_code}")
                    return False
        except asyncio.TimeoutError:
            print("[ERROR] LLM响应超时 - 这可能是死循环的原因!")
            return False
        except Exception as e:
            print(f"[ERROR] LLM测试错误: {e}")
            return False
    
    async def check_running_processes(self):
        """检查正在运行的相关进程"""
        print("[INFO] 检查可能的死循环进程...")
        try:
            import psutil
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] in ['python.exe', 'python']:
                        cmdline = proc.info['cmdline']
                        if cmdline and any(script in str(cmdline) for script in [
                            'test_ollama_integration.py',
                            'interactive_test.py', 
                            'simple_test.py',
                            'main.py'
                        ]):
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'cmdline': ' '.join(cmdline) if cmdline else 'N/A'
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            if python_processes:
                print("[WARN] 发现可能的死循环进程:")
                for proc in python_processes:
                    print(f"   - PID {proc['pid']}: {proc['cmdline']}")
                print("\n[INFO] 要停止这些进程，可以使用:")
                for proc in python_processes:
                    print(f"   - taskkill /F /PID {proc['pid']}")
            else:
                print("[OK] 未发现相关的Python进程")
                
        except ImportError:
            print("[WARN] 需要安装 psutil 来检查进程: pip install psutil")
        except Exception as e:
            print(f"[ERROR] 检查进程时出错: {e}")
    
    async def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始系统诊断...")
        print("=" * 50)
        
        # 设置超时信号处理
        def timeout_handler(signum, frame):
            print("\n❌ 诊断超时，可能存在死循环!")
            sys.exit(1)
        
        # Windows不支持SIGALRM，跳过
        if hasattr(signal, 'SIGALRM'):
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(30)  # 30秒总超时
        
        try:
            results = {
                'ollama': await self.check_ollama(),
                'mock': await self.check_mock_service(), 
                'llm': await self.test_llm_integration()
            }
            
            await self.check_running_processes()
            
            print("\n" + "=" * 50)
            print("📊 诊断结果:")
            for service, status in results.items():
                status_icon = "✅" if status else "❌"
                print(f"   {status_icon} {service.upper()}: {'正常' if status else '异常'}")
            
            if not all(results.values()):
                print("\n💡 建议:")
                if not results['ollama']:
                    print("   - 启动Ollama服务: ollama serve")
                    print("   - 安装Qwen模型: ollama pull qwen3:8b")
                if not results['mock']:
                    print("   - 启动Mock服务: python cdm_api/mock_server.py")
                if not results['llm']:
                    print("   - 检查Ollama模型是否可用")
                    print("   - 可能是模型推理超时导致的死循环")
            
        except KeyboardInterrupt:
            print("\n\n👋 诊断被用户中断")
        except Exception as e:
            print(f"\n❌ 诊断过程出错: {e}")
        finally:
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)  # 取消超时

async def main():
    """主函数"""
    print("CDM AI Agent 死循环诊断工具")
    print("此工具将检查可能导致测试脚本死循环的问题\n")
    
    diagnostics = ServiceDiagnostics()
    await diagnostics.run_diagnosis()
    
    print("\n🎯 如果发现死循环进程，按 Ctrl+C 可以强制退出")
    print("💡 在交互式模式下，输入 'quit' 或 'exit' 可以正常退出")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 诊断工具已退出")
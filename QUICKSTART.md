# 🚀 快速启动指南

## 1️⃣ 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入以下必需配置：
# - 华为云CDM认证信息
# - OpenAI API密钥 或 Claude API密钥
```

## 2️⃣ 配置说明

### 华为云CDM配置
```bash
CDM_HUAWEI_CLOUD_DOMAIN=ap-southeast-1
CDM_HUAWEI_CLOUD_PROJECT_ID=your_project_id
CDM_HUAWEI_CLOUD_USERNAME=your_username
CDM_HUAWEI_CLOUD_PASSWORD=your_password
CDM_HUAWEI_CLOUD_ACCOUNT_NAME=your_account_name
```

### LLM配置（选择一个）

**使用OpenAI：**
```bash
CDM_LLM_PROVIDER=openai
CDM_OPENAI_API_KEY=sk-...
CDM_OPENAI_MODEL=gpt-4-turbo
```

**使用Claude：**
```bash
CDM_LLM_PROVIDER=anthropic
CDM_ANTHROPIC_API_KEY=sk-ant-...
CDM_ANTHROPIC_MODEL=claude-3-sonnet-********
```

## 3️⃣ 启动服务

```bash
# Web界面模式（推荐）
python main.py web

# 或者使用批处理文件
run_web.bat
```

访问：http://localhost:8000

## 4️⃣ 开始对话

在Web界面中尝试以下对话：

```
你: 列出所有集群
你: 创建一个新的集群
你: 我要把MySQL数据迁移到OBS
你: 检查刚才创建的作业状态
```

## 🔧 MCP模式

如果要与Claude Desktop等MCP客户端集成：

```bash
python main.py mcp
```

## 📋 健康检查

访问：http://localhost:8000/api/health

## 🆘 常见问题

1. **LLM API调用失败**：检查API密钥和网络连接
2. **CDM认证失败**：确认华为云账号信息正确
3. **依赖安装问题**：使用Python 3.8+版本
"""
简化的CDM + Ollama集成测试
"""
import asyncio
import json
import httpx
from typing import Dict, List, Any
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

# 基础配置类
class SimpleConfig:
    llm_provider = "ollama"
    ollama_base_url = "http://localhost:11434"
    ollama_model = "qwen3:8b"
    temperature = 0.8
    max_tokens = 2048

config = SimpleConfig()


class SimpleLLMClient:
    """简化的LLM客户端"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=300.0)
        self.base_url = config.ollama_base_url
        self.model = config.ollama_model
    
    async def chat(self, messages: List[Dict], functions: List[Dict] = None) -> Dict:
        """聊天对话"""
        # 转换消息为prompt
        prompt = self._messages_to_prompt(messages)
        
        # 添加工具描述
        if functions:
            tools_desc = self._format_tools(functions)
            prompt = f"{tools_desc}\\n\\n{prompt}"
        
        params = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": config.temperature,
                "num_predict": config.max_tokens,
            }
        }
        
        try:
            response = await self.client.post(f"{self.base_url}/api/generate", json=params)
            response.raise_for_status()
            
            result = response.json()
            content = result.get("response", "")
            
            # 解析工具调用
            tool_calls = self._extract_tool_calls(content)
            
            return {
                "content": content,
                "tool_calls": tool_calls if tool_calls else None,
                "usage": {
                    "prompt_tokens": result.get("prompt_eval_count", 0),
                    "completion_tokens": result.get("eval_count", 0)
                }
            }
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return {"content": f"对不起，我遇到了技术问题: {e}", "tool_calls": None}
    
    def _messages_to_prompt(self, messages: List[Dict]) -> str:
        """转换消息为prompt"""
        parts = []
        for msg in messages:
            role = msg["role"]
            content = msg["content"]
            if role == "system":
                parts.append(f"System: {content}")
            elif role == "user":
                parts.append(f"Human: {content}")
            elif role == "assistant":
                parts.append(f"Assistant: {content}")
        
        parts.append("Assistant:")
        return "\\n\\n".join(parts)
    
    def _format_tools(self, functions: List[Dict]) -> str:
        """格式化工具描述"""
        lines = ["You have access to the following tools:"]
        
        for func in functions:
            name = func["name"]
            desc = func["description"]
            lines.append(f"- {name}: {desc}")
        
        lines.append("""
To use a tool, respond with a JSON object like:
{
  "tool_call": {
    "name": "tool_name",
    "arguments": {"param": "value"}
  },
  "explanation": "why you're using this tool"
}

If you don't need tools, respond normally.""")
        
        return "\\n".join(lines)
    
    def _extract_tool_calls(self, content: str) -> List[Dict]:
        """提取工具调用"""
        if "tool_call" not in content:
            return []
        
        try:
            # 查找JSON部分
            start = content.find("{")
            end = content.rfind("}") + 1
            if start == -1 or end == -1:
                return []
            
            json_str = content[start:end]
            parsed = json.loads(json_str)
            
            if "tool_call" in parsed:
                tool_call = parsed["tool_call"]
                return [{
                    "id": "call_1",
                    "type": "function",
                    "function": {
                        "name": tool_call["name"],
                        "arguments": json.dumps(tool_call["arguments"])
                    }
                }]
        except:
            pass
        
        return []


class SimpleCDMTools:
    """简化的CDM工具"""
    
    def __init__(self):
        self.mock_url = "http://localhost:8000"
        self.project_id = "test-project-id"
        self.client = httpx.AsyncClient(timeout=30.0)
    
    def get_functions(self) -> List[Dict]:
        """获取工具函数定义"""
        return [
            {
                "name": "list_clusters",
                "description": "查询CDM集群列表",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_cluster",
                "description": "查询集群详细信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {"type": "string", "description": "集群ID"}
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "list_connections",
                "description": "查询集群的连接列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {"type": "string", "description": "集群ID"}
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "list_jobs",
                "description": "查询集群的作业列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {"type": "string", "description": "集群ID"}
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "get_job_status",
                "description": "查询作业执行状态",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {"type": "string", "description": "集群ID"},
                        "job_name": {"type": "string", "description": "作业名称"}
                    },
                    "required": ["cluster_id", "job_name"]
                }
            }
        ]
    
    async def execute(self, function_name: str, arguments: Dict) -> Dict:
        """执行工具函数"""
        try:
            print(f"[TOOL] 执行工具: {function_name}，参数: {arguments}")
            
            if function_name == "list_clusters":
                return await self._list_clusters()
            elif function_name == "get_cluster":
                return await self._get_cluster(arguments["cluster_id"])
            elif function_name == "list_connections":
                return await self._list_connections(arguments["cluster_id"])
            elif function_name == "list_jobs":
                return await self._list_jobs(arguments["cluster_id"])
            elif function_name == "get_job_status":
                return await self._get_job_status(arguments["cluster_id"], arguments["job_name"])
            else:
                return {"error": f"未知工具: {function_name}"}
        except Exception as e:
            print(f"[ERROR] 工具执行失败: {e}")
            return {"error": str(e)}
    
    async def _list_clusters(self) -> Dict:
        """查询集群列表"""
        response = await self.client.get(f"{self.mock_url}/v1.1/{self.project_id}/clusters")
        data = response.json()
        return {
            "success": True,
            "message": f"找到 {data['total']} 个集群",
            "data": data
        }
    
    async def _get_cluster(self, cluster_id: str) -> Dict:
        """查询集群详情"""
        response = await self.client.get(f"{self.mock_url}/v1.1/{self.project_id}/clusters/{cluster_id}")
        data = response.json()
        return {
            "success": True,
            "message": f"获取集群 {cluster_id} 详细信息",
            "data": data
        }
    
    async def _list_connections(self, cluster_id: str) -> Dict:
        """查询连接列表"""
        response = await self.client.get(f"{self.mock_url}/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link")
        data = response.json()
        return {
            "success": True,
            "message": f"集群 {cluster_id} 有 {len(data['links'])} 个连接",
            "data": data
        }
    
    async def _list_jobs(self, cluster_id: str) -> Dict:
        """查询作业列表"""
        response = await self.client.get(f"{self.mock_url}/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job")
        data = response.json()
        return {
            "success": True,
            "message": f"集群 {cluster_id} 有 {len(data['jobs'])} 个作业",
            "data": data
        }
    
    async def _get_job_status(self, cluster_id: str, job_name: str) -> Dict:
        """查询作业状态"""
        response = await self.client.get(f"{self.mock_url}/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}/status")
        data = response.json()
        return {
            "success": True,
            "message": f"作业 {job_name} 状态: {data['status']}",
            "data": data
        }


class CDMAgent:
    """CDM智能代理"""
    
    def __init__(self):
        self.llm = SimpleLLMClient()
        self.tools = SimpleCDMTools()
        
        self.system_message = """你是华为云CDM（云数据迁移）服务的智能助手。

你可以帮助用户：
1. 查询和管理CDM集群
2. 查看数据库连接
3. 管理数据迁移作业
4. 监控作业状态

请用简洁、专业的中文回答用户问题。如果需要调用API，请使用提供的工具。"""
    
    async def chat(self, user_input: str) -> str:
        """处理用户输入"""
        messages = [
            {"role": "system", "content": self.system_message},
            {"role": "user", "content": user_input}
        ]
        
        functions = self.tools.get_functions()
        
        print(f"\\n[USER] 用户: {user_input}")
        print("[THINK] 正在思考...")
        
        try:
            # 调用LLM
            response = await self.llm.chat(messages, functions)
            
            # 处理工具调用
            if response.get("tool_calls"):
                tool_results = []
                for tool_call in response["tool_calls"]:
                    func_name = tool_call["function"]["name"]
                    func_args = json.loads(tool_call["function"]["arguments"])
                    
                    result = await self.tools.execute(func_name, func_args)
                    tool_results.append(result)
                
                # 生成最终回复
                if tool_results:
                    results_summary = []
                    for result in tool_results:
                        if result.get("success"):
                            results_summary.append(f"[OK] {result.get('message', '操作成功')}")
                        else:
                            results_summary.append(f"[ERROR] {result.get('error', '操作失败')}")
                    
                    # 再次询问LLM生成用户友好的回复
                    follow_up_messages = messages + [
                        {"role": "assistant", "content": response["content"]},
                        {"role": "user", "content": f"工具执行结果：{'; '.join(results_summary)}。请基于这些结果给用户一个简洁的回复。"}
                    ]
                    
                    final_response = await self.llm.chat(follow_up_messages)
                    return final_response["content"]
            
            return response["content"]
            
        except Exception as e:
            print(f"[ERROR] 处理失败: {e}")
            return f"抱歉，处理您的请求时遇到了问题: {str(e)}"


async def main():
    """主函数"""
    print("[START] CDM + Ollama 智能助手启动！")
    print("[INFO] 我可以帮您管理华为云CDM服务")
    print("[INFO] 输入 'quit' 退出\\n")
    
    agent = CDMAgent()
    
    # 自动测试用例
    test_cases = [
        "你好，请介绍一下你的功能",
        "查询当前有哪些CDM集群",
        "查看集群 test-cluster-id 的详细信息",
        "查询集群 test-cluster-id 的连接列表",
        "查询集群 test-cluster-id 的作业列表"
    ]
    
    print("[TEST] 开始自动测试...")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\\n{'='*50}")
        print(f"测试 {i}: {test_case}")
        print(f"{'='*50}")
        
        try:
            response = await agent.chat(test_case)
            print(f"[REPLY] 助手: {response}")
            
            await asyncio.sleep(2)  # 避免请求过快
            
        except Exception as e:
            print(f"[ERROR] 测试失败: {e}")
    
    print(f"\\n{'='*50}")
    print("[DONE] 自动测试完成！现在可以交互对话")
    print(f"{'='*50}\\n")
    
    # 交互模式
    while True:
        try:
            user_input = input("[YOU] ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("\\n[BYE] 再见！")
                break
            elif not user_input:
                continue
            
            response = await agent.chat(user_input)
            print(f"[AI] 助手: {response}\\n")
            
        except KeyboardInterrupt:
            print("\\n\\n[BYE] 再见！")
            break
        except Exception as e:
            print(f"[ERROR] 错误: {e}\\n")


if __name__ == "__main__":
    print("[INFO] 检查服务状态...")
    
    # 检查Ollama
    try:
        import httpx
        async def check_services():
            async with httpx.AsyncClient() as client:
                # 检查Ollama
                try:
                    response = await client.get("http://localhost:11434/api/tags")
                    models = response.json().get("models", [])
                    qwen_models = [m["name"] for m in models if "qwen" in m["name"].lower()]
                    if qwen_models:
                        print(f"[OK] Ollama服务正常，Qwen模型: {qwen_models}")
                    else:
                        print("[WARN] 未找到Qwen模型")
                except Exception as e:
                    print(f"[ERROR] Ollama服务异常: {e}")
                    return False
                
                # 检查MOCK服务
                try:
                    response = await client.get("http://localhost:8000/")
                    print("[OK] CDM MOCK服务正常")
                except Exception as e:
                    print(f"[WARN] CDM MOCK服务异常: {e}")
                
                return True
        
        if asyncio.run(check_services()):
            print("\\n[START] 环境就绪，开始测试...")
            asyncio.run(main())
        else:
            print("\\n[ERROR] 环境检查失败")
            
    except Exception as e:
        print(f"[ERROR] 启动失败: {e}")
"""
华为云CDM API客户端
"""
import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin

import httpx
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from .auth import HuaweiCloudAuth, MockAuth
from .exceptions import (
    CDMAPIError, CDMTimeoutError, CDMAuthError, CDMClusterError,
    CDMConnectionError, CDMJobError, handle_exception, log_and_raise
)
from .models import (
    Cluster, CreateClusterRequest, Connection, CreateConnectionRequest,
    Job, CreateJobRequest, JobExecution, SubmissionResponse, APIResponse, ListResponse
)


class CDMClient:
    """华为云CDM API客户端"""
    
    def __init__(self, username: str = None, password: str = None, 
                 domain_name: str = None, project_id: str = None,
                 iam_endpoint: str = None, cdm_endpoint: str = None,
                 mock_mode: bool = False):
        """
        初始化CDM客户端
        
        Args:
            username: 华为云用户名
            password: 华为云密码
            domain_name: 华为云域名
            project_id: 华为云项目ID
            iam_endpoint: IAM服务端点
            cdm_endpoint: CDM服务端点
            mock_mode: 是否使用mock模式（用于测试）
        """
        self.username = username or config.huawei_cloud_username
        self.password = password or config.huawei_cloud_password
        self.domain_name = domain_name or config.huawei_cloud_account_name
        self.project_id = project_id or config.huawei_cloud_project_id
        self.iam_endpoint = iam_endpoint or config.iam_endpoint
        self.cdm_endpoint = cdm_endpoint or config.cdm_endpoint
        self.mock_mode = mock_mode
        
        # 根据模式选择认证方式
        if self.mock_mode:
            logger.info("CDM client initialized in mock mode")
            self.auth = MockAuth(mock_mode=True)
            # 在mock模式下，确保使用mock服务的端点
            if not cdm_endpoint and hasattr(config, 'mock_cdm_endpoint'):
                self.cdm_endpoint = config.mock_cdm_endpoint
        else:
            self.auth = HuaweiCloudAuth(
                self.username, self.password, self.domain_name, 
                self.project_id, self.iam_endpoint
            )
        
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @handle_exception
    async def _request(self, method: str, path: str, **kwargs) -> Dict[str, Any]:
        """执行HTTP请求"""
        url = urljoin(self.cdm_endpoint, path)
        
        try:
            token = await self.auth.get_token()
            headers = self.auth.get_auth_headers(token)
            
            if 'headers' in kwargs:
                headers.update(kwargs['headers'])
            kwargs['headers'] = headers
            
            async with httpx.AsyncClient(timeout=config.request_timeout) as client:
                response = await client.request(method, url, **kwargs)
                
            await self._check_response(response)
            
            if response.content:
                return response.json()
            return {}
            
        except httpx.TimeoutException as e:
            raise CDMTimeoutError(
                f"Request timeout: {method} {url}",
                timeout_seconds=config.request_timeout,
                operation=f"{method} {path}",
                cause=e
            )
        except CDMAuthError:
            raise
        except Exception as e:
            raise CDMAPIError(
                f"Request failed: {str(e)}",
                request_url=url,
                cause=e
            )
            
    async def _check_response(self, response: httpx.Response):
        """检查响应状态"""
        if response.status_code >= 400:
            try:
                error_data = response.json()
                error_message = error_data.get('error_msg', error_data.get('message', response.text))
                error_code = error_data.get('error_code')
            except Exception as parse_error:
                logger.warning(f"Failed to parse error response: {parse_error}")
                error_message = response.text
                error_code = None
                
            raise CDMAPIError(
                f"API request failed: {error_message}",
                status_code=response.status_code,
                error_code=error_code,
                request_url=str(response.url),
                response_body=response.text
            )
            
    # 集群管理API
    @handle_exception
    async def list_clusters(self, page: int = 1, size: int = 100) -> ListResponse:
        """查询集群列表"""
        params = {
            "offset": (page - 1) * size,
            "limit": size
        }
        
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters", params=params)
        
        clusters = [Cluster(**cluster) for cluster in data.get('clusters', [])]
        
        return ListResponse(
            total=data.get('total', 0),
            items=clusters,
            page=page,
            size=size
        )
        
    @handle_exception
    async def get_cluster(self, cluster_id: str) -> Cluster:
        """查询集群详情"""
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters/{cluster_id}")
        return Cluster(**data.get('cluster', {}))
        
    @handle_exception
    async def create_cluster(self, request: CreateClusterRequest) -> str:
        """创建集群"""
        data = await self._request(
            "POST", 
            f"/v1.1/{self.project_id}/clusters",
            json=request.dict()
        )
        return data.get('id')
        
    @handle_exception
    async def delete_cluster(self, cluster_id: str) -> bool:
        """删除集群"""
        await self._request("DELETE", f"/v1.1/{self.project_id}/clusters/{cluster_id}")
        return True
        
    @handle_exception
    async def restart_cluster(self, cluster_id: str) -> bool:
        """重启集群"""
        data = {"restart": {}}
        await self._request(
            "POST", 
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/action",
            json=data
        )
        return True
        
    @handle_exception
    async def start_cluster(self, cluster_id: str) -> bool:
        """启动集群"""
        data = {"start": {}}
        await self._request(
            "POST", 
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/action",
            json=data
        )
        return True
        
    @handle_exception
    async def stop_cluster(self, cluster_id: str) -> bool:
        """停止集群"""
        data = {"stop": {}}
        await self._request(
            "POST", 
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/action",
            json=data
        )
        return True
        
    # 连接管理API
    @handle_exception
    async def list_connections(self, cluster_id: str) -> List[Connection]:
        """查询连接列表"""
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link")
        return [Connection(**conn) for conn in data.get('links', [])]
        
    @handle_exception
    async def get_connection(self, cluster_id: str, connection_name: str) -> Connection:
        """查询连接详情"""
        data = await self._request(
            "GET", 
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link/{connection_name}"
        )
        return Connection(**data.get('link', {}))
        
    @handle_exception
    async def create_connection(self, cluster_id: str, request: CreateConnectionRequest) -> bool:
        """创建连接"""
        await self._request(
            "POST",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link",
            json=request.dict()
        )
        return True
        
    @handle_exception
    async def update_connection(self, cluster_id: str, connection_name: str, 
                              connection: Connection) -> bool:
        """更新连接"""
        await self._request(
            "PUT",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link/{connection_name}",
            json={"links": [connection.dict()]}
        )
        return True
        
    @handle_exception
    async def delete_connection(self, cluster_id: str, connection_name: str) -> bool:
        """删除连接"""
        await self._request(
            "DELETE",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/link/{connection_name}"
        )
        return True
        
    # 作业管理API
    @handle_exception
    async def list_jobs(self, cluster_id: str) -> List[Job]:
        """查询作业列表"""
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job")
        return [Job(**job) for job in data.get('jobs', [])]
        
    @handle_exception
    async def get_job(self, cluster_id: str, job_name: str) -> Job:
        """查询作业详情"""
        data = await self._request(
            "GET", 
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}"
        )
        return Job(**data.get('job', {}))
        
    @handle_exception
    async def create_job(self, cluster_id: str, request: CreateJobRequest) -> bool:
        """创建作业"""
        await self._request(
            "POST",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job",
            json=request.dict()
        )
        return True
        
    @handle_exception
    async def update_job(self, cluster_id: str, job_name: str, job: Job) -> bool:
        """更新作业"""
        await self._request(
            "PUT",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}",
            json={"jobs": [job.dict()]}
        )
        return True
        
    @handle_exception
    async def delete_job(self, cluster_id: str, job_name: str) -> bool:
        """删除作业"""
        await self._request(
            "DELETE",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}"
        )
        return True
        
    @handle_exception
    async def start_job(self, cluster_id: str, job_name: str) -> SubmissionResponse:
        """启动作业"""
        data = await self._request(
            "PUT",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}/start"
        )
        return SubmissionResponse(**data)
        
    @handle_exception
    async def stop_job(self, cluster_id: str, job_name: str) -> bool:
        """停止作业"""
        await self._request(
            "PUT",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}/stop"
        )
        return True
        
    @handle_exception
    async def get_job_status(self, cluster_id: str, job_name: str) -> Dict[str, Any]:
        """查询作业状态"""
        data = await self._request(
            "GET",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}/status"
        )
        return data
        
    @handle_exception
    async def get_job_executions(self, cluster_id: str, job_name: str) -> List[JobExecution]:
        """查询作业执行历史"""
        data = await self._request(
            "GET",
            f"/v1.1/{self.project_id}/clusters/{cluster_id}/cdm/job/{job_name}/submissions"
        )
        return [JobExecution(**execution) for execution in data.get('submissions', [])]
        
    # 随机集群作业操作
    @handle_exception
    async def create_and_start_random_cluster_job(self, job: Job) -> SubmissionResponse:
        """随机集群创建并启动作业"""
        data = await self._request(
            "POST",
            f"/v1.1/{self.project_id}/clusters/job",
            json={"jobs": [job.dict()]}
        )
        return SubmissionResponse(**data)
        
    @handle_exception
    async def stop_random_cluster_job(self, job_name: str) -> bool:
        """停止随机集群作业"""
        await self._request(
            "PUT",
            f"/v1.1/{self.project_id}/clusters/job/{job_name}/stop"
        )
        return True
        
    # 辅助方法
    @handle_exception
    async def wait_cluster_ready(self, cluster_id: str, timeout: int = 1800) -> bool:
        """等待集群就绪"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                cluster = await self.get_cluster(cluster_id)
                if cluster.status == "200":  # NORMAL状态
                    return True
                elif cluster.status in ["300", "303"]:  # FAILED或FROZEN状态
                    return False
                    
                await asyncio.sleep(30)  # 等待30秒后重试
            except Exception as e:
                logger.warning(f"Error checking cluster status: {e}")
                await asyncio.sleep(30)
                
        return False
        
    @handle_exception
    async def get_cluster_versions(self) -> List[str]:
        """获取支持的集群版本列表"""
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters/versions")
        return data.get('versions', [])
        
    @handle_exception
    async def get_cluster_flavors(self) -> List[Dict]:
        """获取集群规格列表"""
        data = await self._request("GET", f"/v1.1/{self.project_id}/clusters/flavors")
        return data.get('flavors', [])
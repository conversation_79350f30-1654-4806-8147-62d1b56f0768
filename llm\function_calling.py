"""
Function Calling处理器
"""
import json
from typing import Dict, List, Optional, Any, Callable, Awaitable
from dataclasses import dataclass

from loguru import logger
from mcp_server.tools import CDMTools


@dataclass
class FunctionCall:
    """函数调用"""
    id: str
    name: str
    arguments: Dict[str, Any]


@dataclass
class FunctionResult:
    """函数调用结果"""
    call_id: str
    name: str
    success: bool
    result: Any
    error: Optional[str] = None


class FunctionCallHandler:
    """Function Calling处理器"""
    
    def __init__(self, cdm_tools: CDMTools):
        self.cdm_tools = cdm_tools
        self._function_registry = self._build_function_registry()
    
    def _build_function_registry(self) -> Dict[str, Dict]:
        """构建函数注册表"""
        # 获取CDM工具的函数定义
        tools = self.cdm_tools.get_tool_definitions()
        
        function_registry = {}
        for tool in tools:
            function_def = {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema
            }
            function_registry[tool.name] = function_def
        
        return function_registry
    
    def get_available_functions(self) -> List[Dict]:
        """获取可用函数列表"""
        return list(self._function_registry.values())
    
    def parse_function_calls(self, tool_calls: List[Dict]) -> List[FunctionCall]:
        """解析函数调用"""
        function_calls = []
        
        for tool_call in tool_calls:
            try:
                call_id = tool_call.get("id", "")
                function_info = tool_call.get("function", {})
                name = function_info.get("name", "")
                
                # 解析参数
                arguments_str = function_info.get("arguments", "{}")
                if isinstance(arguments_str, str):
                    arguments = json.loads(arguments_str)
                else:
                    arguments = arguments_str
                
                function_calls.append(FunctionCall(
                    id=call_id,
                    name=name,
                    arguments=arguments
                ))
                
            except Exception as e:
                logger.error(f"解析函数调用失败: {e}")
                continue
        
        return function_calls
    
    async def execute_function_calls(self, function_calls: List[FunctionCall]) -> List[FunctionResult]:
        """执行函数调用"""
        results = []
        
        for call in function_calls:
            result = await self._execute_single_function(call)
            results.append(result)
        
        return results
    
    async def _execute_single_function(self, call: FunctionCall) -> FunctionResult:
        """执行单个函数调用"""
        try:
            logger.info(f"执行函数: {call.name} 参数: {call.arguments}")
            
            # 检查函数是否存在
            if call.name not in self._function_registry:
                return FunctionResult(
                    call_id=call.id,
                    name=call.name,
                    success=False,
                    error=f"未知函数: {call.name}"
                )
            
            # 调用CDM工具
            tool_result = await self.cdm_tools.call_tool(call.name, call.arguments)
            
            # 处理结果
            if tool_result and len(tool_result) > 0:
                result_content = tool_result[0].text
                
                # 尝试解析JSON结果
                try:
                    parsed_result = json.loads(result_content)
                except json.JSONDecodeError:
                    parsed_result = result_content
                
                return FunctionResult(
                    call_id=call.id,
                    name=call.name,
                    success=True,
                    result=parsed_result
                )
            else:
                return FunctionResult(
                    call_id=call.id,
                    name=call.name,
                    success=True,
                    result="操作完成"
                )
                
        except Exception as e:
            logger.error(f"函数调用执行失败: {e}")
            return FunctionResult(
                call_id=call.id,
                name=call.name,
                success=False,
                error=str(e)
            )
    
    def format_function_results_for_llm(self, results: List[FunctionResult]) -> List[Dict[str, str]]:
        """格式化函数结果供LLM使用"""
        messages = []
        
        for result in results:
            if result.success:
                content = self._format_success_result(result)
            else:
                content = f"函数调用失败: {result.error}"
            
            messages.append({
                "role": "tool",
                "tool_call_id": result.call_id,
                "name": result.name,
                "content": content
            })
        
        return messages
    
    def _format_success_result(self, result: FunctionResult) -> str:
        """格式化成功结果"""
        if isinstance(result.result, dict):
            # 格式化字典结果为更友好的文本
            return self._format_dict_result(result.result, result.name)
        elif isinstance(result.result, list):
            return self._format_list_result(result.result, result.name)
        else:
            return str(result.result)
    
    def _format_dict_result(self, data: Dict, function_name: str) -> str:
        """格式化字典结果"""
        if function_name.startswith("cdm_list_"):
            return self._format_list_response(data)
        elif function_name.startswith("cdm_get_"):
            return self._format_detail_response(data)
        elif function_name.startswith("cdm_create_"):
            return self._format_create_response(data)
        else:
            return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _format_list_response(self, data: Dict) -> str:
        """格式化列表响应"""
        if "clusters" in data:
            clusters = data["clusters"]
            result = f"找到 {len(clusters)} 个集群:\n"
            for cluster in clusters:
                status_text = self._get_cluster_status_text(cluster.get("status"))
                result += f"- {cluster.get('name', 'N/A')} (ID: {cluster.get('id', 'N/A')}, 状态: {status_text})\n"
            return result
            
        elif "connections" in data or isinstance(data, list):
            connections = data.get("connections", data) if isinstance(data, dict) else data
            result = f"找到 {len(connections)} 个连接:\n"
            for conn in connections:
                result += f"- {conn.get('name', 'N/A')} ({conn.get('connector_name', 'N/A')})\n"
            return result
            
        elif "jobs" in data or isinstance(data, list):
            jobs = data.get("jobs", data) if isinstance(data, dict) else data
            result = f"找到 {len(jobs)} 个作业:\n"
            for job in jobs:
                result += f"- {job.get('name', 'N/A')} (类型: {job.get('type', 'N/A')})\n"
            return result
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _format_detail_response(self, data: Dict) -> str:
        """格式化详情响应"""
        if "id" in data and "name" in data:  # 集群详情
            status_text = self._get_cluster_status_text(data.get("status"))
            result = f"集群信息:\n"
            result += f"- 名称: {data.get('name')}\n"
            result += f"- ID: {data.get('id')}\n" 
            result += f"- 状态: {status_text}\n"
            result += f"- 模式: {data.get('mode', 'N/A')}\n"
            result += f"- 版本: {data.get('version', 'N/A')}\n"
            if data.get('created_at'):
                result += f"- 创建时间: {data.get('created_at')}\n"
            return result
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _format_create_response(self, data: Dict) -> str:
        """格式化创建响应"""
        if "id" in data:
            return f"创建成功，ID: {data['id']}"
        elif "submission_id" in data:
            return f"提交成功，提交ID: {data['submission_id']}"
        else:
            return "创建成功"
    
    def _format_list_result(self, data: List, function_name: str) -> str:
        """格式化列表结果"""
        if function_name == "cdm_get_cluster_versions":
            return f"支持的集群版本: {', '.join(data)}"
        elif function_name == "cdm_get_cluster_flavors":
            return f"可用集群规格: {len(data)} 种"
        else:
            return f"返回 {len(data)} 项结果"
    
    def _get_cluster_status_text(self, status: str) -> str:
        """获取集群状态文本"""
        status_map = {
            "100": "创建中",
            "200": "正常",
            "300": "失败", 
            "303": "冻结",
            "800": "删除中"
        }
        return status_map.get(status, status or "未知")
    
    def get_function_definition(self, function_name: str) -> Optional[Dict]:
        """获取函数定义"""
        return self._function_registry.get(function_name)
    
    def validate_function_call(self, call: FunctionCall) -> Optional[str]:
        """验证函数调用"""
        func_def = self.get_function_definition(call.name)
        if not func_def:
            return f"未知函数: {call.name}"
        
        # 检查必需参数
        parameters = func_def.get("parameters", {})
        required_params = parameters.get("required", [])
        
        for param in required_params:
            if param not in call.arguments:
                return f"缺少必需参数: {param}"
        
        return None
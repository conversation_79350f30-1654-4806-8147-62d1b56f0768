"""
测试CDM MOCK服务的脚本
"""
import asyncio
import json
from typing import Dict, Any
import httpx
from loguru import logger

# 配置日志
logger.add("test_mock_server.log", rotation="10 MB", level="INFO")

class MockCDMTester:
    """CDM Mock服务测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.project_id = "test-project-id"
        self.cluster_id = "test-cluster-id"
    
    async def test_cluster_apis(self):
        """测试集群相关API"""
        logger.info("开始测试集群相关API...")
        
        async with httpx.AsyncClient() as client:
            # 测试查询集群列表
            logger.info("测试查询集群列表")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters")
            logger.info(f"集群列表响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试查询集群详情
            logger.info("测试查询集群详情")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}")
            logger.info(f"集群详情响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试创建集群
            logger.info("测试创建集群")
            create_data = {
                "cluster": {
                    "name": "test-cluster",
                    "mode": "sharding",
                    "datastore": {
                        "type": "cdm",
                        "version": "*********"
                    },
                    "flavor": "cdm.medium",
                    "instances": 3,
                    "availabilityZone": "cn-north-1a",
                    "vpc": "vpc-12345678",
                    "subnet": "subnet-12345678",
                    "securityGroup": "sg-12345678",
                    "publicIp": False,
                    "enterpriseProjectId": "0"
                }
            }
            response = await client.post(
                f"{self.base_url}/v1.1/{self.project_id}/clusters",
                json=create_data
            )
            logger.info(f"创建集群响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试集群操作
            logger.info("测试集群重启操作")
            action_data = {
                "restart": {
                    "instance": "",
                    "type": "cdm",
                    "group": ""
                }
            }
            response = await client.post(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/action",
                json=action_data
            )
            logger.info(f"集群操作响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    async def test_connection_apis(self):
        """测试连接相关API"""
        logger.info("开始测试连接相关API...")
        
        async with httpx.AsyncClient() as client:
            # 测试查询连接列表
            logger.info("测试查询连接列表")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/link")
            logger.info(f"连接列表响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试查询连接详情
            connection_name = "mysql-connection"
            logger.info("测试查询连接详情")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/link/{connection_name}")
            logger.info(f"连接详情响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试创建连接
            logger.info("测试创建连接")
            create_connection_data = {
                "links": [
                    {
                        "name": "test-mysql-connection",
                        "connector-name": "generic-jdbc-connector",
                        "link-config-values": {
                            "configs": [
                                {
                                    "inputs": [
                                        {"name": "linkConfig.databaseType", "value": "MYSQL"},
                                        {"name": "linkConfig.host", "value": "*************"},
                                        {"name": "linkConfig.port", "value": "3306"},
                                        {"name": "linkConfig.database", "value": "testdb"},
                                        {"name": "linkConfig.username", "value": "root"},
                                        {"name": "linkConfig.password", "value": "password"}
                                    ],
                                    "name": "linkConfig"
                                }
                            ]
                        }
                    }
                ]
            }
            response = await client.post(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/link",
                json=create_connection_data
            )
            logger.info(f"创建连接响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    async def test_job_apis(self):
        """测试作业相关API"""
        logger.info("开始测试作业相关API...")
        
        async with httpx.AsyncClient() as client:
            # 测试查询作业列表
            logger.info("测试查询作业列表")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job")
            logger.info(f"作业列表响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试查询作业详情
            job_name = "mysql2dws-job"
            logger.info("测试查询作业详情")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job/{job_name}")
            logger.info(f"作业详情响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试创建作业
            logger.info("测试创建作业")
            create_job_data = {
                "jobs": [
                    {
                        "name": "test-mysql2dws-job",
                        "type": "NORMAL_JOB",
                        "from-connector-name": "generic-jdbc-connector",
                        "to-connector-name": "generic-jdbc-connector",
                        "from-link-name": "mysql-connection",
                        "to-link-name": "dws-connection",
                        "from-config-values": {
                            "configs": [
                                {
                                    "inputs": [
                                        {"name": "fromJobConfig.useSql", "value": "false"},
                                        {"name": "fromJobConfig.schemaName", "value": "testdb"},
                                        {"name": "fromJobConfig.tableName", "value": "users"},
                                        {"name": "fromJobConfig.columnList", "value": "id&name&email&created_at"},
                                        {"name": "fromJobConfig.incrMigration", "value": "false"}
                                    ],
                                    "name": "fromJobConfig"
                                }
                            ]
                        },
                        "to-config-values": {
                            "configs": [
                                {
                                    "inputs": [
                                        {"name": "toJobConfig.schemaName", "value": "public"},
                                        {"name": "toJobConfig.tablePreparation", "value": "DROP_AND_CREATE"},
                                        {"name": "toJobConfig.tableName", "value": "users_dws"}
                                    ],
                                    "name": "toJobConfig"
                                }
                            ]
                        }
                    }
                ]
            }
            response = await client.post(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job",
                json=create_job_data
            )
            logger.info(f"创建作业响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试启动作业
            logger.info("测试启动作业")
            response = await client.put(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job/{job_name}/start"
            )
            logger.info(f"启动作业响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试查询作业状态
            logger.info("测试查询作业状态")
            response = await client.get(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job/{job_name}/status"
            )
            logger.info(f"作业状态响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试查询作业执行历史
            logger.info("测试查询作业执行历史")
            response = await client.get(
                f"{self.base_url}/v1.1/{self.project_id}/clusters/{self.cluster_id}/cdm/job/{job_name}/submissions"
            )
            logger.info(f"作业执行历史响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    async def test_auxiliary_apis(self):
        """测试辅助API"""
        logger.info("开始测试辅助API...")
        
        async with httpx.AsyncClient() as client:
            # 测试获取集群版本列表
            logger.info("测试获取集群版本列表")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/versions")
            logger.info(f"版本列表响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            
            # 测试获取集群规格列表
            logger.info("测试获取集群规格列表")
            response = await client.get(f"{self.base_url}/v1.1/{self.project_id}/clusters/flavors")
            logger.info(f"规格列表响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        logger.info("开始测试错误处理...")
        
        async with httpx.AsyncClient() as client:
            # 测试404错误
            logger.info("测试404错误")
            response = await client.get(f"{self.base_url}/nonexistent/endpoint")
            logger.info(f"404错误响应: {response.status_code}")
            logger.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行CDM Mock服务测试...")
        
        try:
            await self.test_cluster_apis()
            await self.test_connection_apis()
            await self.test_job_apis()
            await self.test_auxiliary_apis()
            await self.test_error_handling()
            logger.info("所有测试完成!")
        except Exception as e:
            logger.error(f"测试过程中出现错误: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")


async def main():
    """主函数"""
    # 检查Mock服务是否运行
    logger.info("检查Mock服务是否运行...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/")
            logger.info(f"Mock服务状态: {response.status_code}")
            logger.info(f"服务信息: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        logger.error(f"无法连接到Mock服务: {e}")
        logger.error("请先启动Mock服务: python cdm_api/mock_server.py")
        return
    
    # 运行测试
    tester = MockCDMTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
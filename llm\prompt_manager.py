"""
Prompt管理器
"""
from typing import Dict, List, Optional, Any
from datetime import datetime

from config import config


class PromptManager:
    """Prompt管理器"""
    
    def __init__(self):
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return """你是华为云CDM（云数据迁移）服务的智能助手。你的主要职责是帮助用户管理CDM集群、数据连接和迁移作业。

## 你的能力

### 集群管理
- 创建、删除、启动、停止、重启CDM集群
- 查询集群列表和详细信息
- 获取集群版本和规格信息
- 等待集群状态变更完成

### 连接管理
- 创建、更新、删除各种数据源连接
- 支持的数据源包括：MySQL、Oracle、PostgreSQL、SQL Server、MongoDB、Redis、OBS、HDFS、Hive、HBase、FTP、SFTP、Kafka等
- 查询连接列表和详细配置

### 作业管理
- 创建、更新、删除数据迁移作业
- 启动、停止作业执行
- 查询作业状态和执行历史
- 实时监控作业进度

### 数据迁移
- 端到端的数据迁移流程
- 自动化配置源和目标连接
- 智能作业配置和优化建议

## 交互原则

1. **理解用户意图**：准确理解用户的需求，无论是简单的查询还是复杂的操作流程
2. **智能推理**：根据上下文推断缺失的参数，提供合理的默认值
3. **逐步执行**：对于复杂任务，分解为多个步骤并逐步执行
4. **结果解释**：用友好的语言解释API返回的结果，突出重要信息
5. **错误处理**：当操作失败时，提供清晰的错误说明和解决建议
6. **主动建议**：基于当前状态主动提供有用的建议

## 函数调用指南

- 优先使用最直接的函数完成用户请求
- 当需要多个步骤时，合理安排函数调用顺序
- 对于创建操作，确保提供必要的参数
- 对于查询操作，提供结构化的结果展示
- 处理依赖关系，如创建作业前需要确保连接存在

## 响应风格

- 使用友好、专业的语调
- 提供具体、可操作的信息
- 适当使用表格或列表格式展示数据
- 在操作完成后提供状态确认
- 主动询问是否需要进一步的帮助

请始终保持专业、准确、有帮助的态度，确保用户能够高效地管理他们的CDM资源。"""

    def create_user_message(self, content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """创建用户消息"""
        message_content = content
        
        # 添加上下文信息
        if context:
            context_info = self._format_context(context)
            if context_info:
                message_content = f"{content}\n\n{context_info}"
        
        return {
            "role": "user",
            "content": message_content
        }
    
    def create_system_message(self) -> Dict[str, str]:
        """创建系统消息"""
        return {
            "role": "system", 
            "content": self.system_prompt
        }
    
    def create_assistant_message(self, content: str, tool_calls: Optional[List[Dict]] = None) -> Dict[str, str]:
        """创建助手消息"""
        message = {
            "role": "assistant",
            "content": content
        }
        
        if tool_calls:
            message["tool_calls"] = tool_calls
            
        return message
    
    def create_function_result_message(self, tool_call_id: str, name: str, content: str) -> Dict[str, str]:
        """创建函数结果消息"""
        return {
            "role": "tool",
            "tool_call_id": tool_call_id,
            "name": name,
            "content": content
        }
    
    def _format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文"""
        context_parts = []
        
        # 当前时间
        context_parts.append(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 当前集群信息
        if "current_cluster_id" in context:
            context_parts.append(f"当前集群: {context['current_cluster_id']}")
        
        # 最近的操作结果
        if "last_operation" in context:
            context_parts.append(f"上次操作: {context['last_operation']}")
        
        # 用户偏好
        if "user_preferences" in context:
            prefs = context["user_preferences"]
            if "default_cluster_mode" in prefs:
                context_parts.append(f"偏好集群模式: {prefs['default_cluster_mode']}")
            if "default_version" in prefs:
                context_parts.append(f"偏好版本: {prefs['default_version']}")
        
        return "\n".join(f"[上下文] {part}" for part in context_parts) if context_parts else ""
    
    def create_error_handling_prompt(self, error_message: str, operation: str) -> str:
        """创建错误处理提示"""
        return f"""执行操作"{operation}"时遇到错误：{error_message}

请分析错误原因并提供解决建议。如果需要重试，请使用正确的参数。
如果错误无法解决，请向用户说明情况并建议替代方案。"""

    def create_confirmation_prompt(self, operation: str, details: Dict[str, Any]) -> str:
        """创建确认提示"""
        details_text = "\n".join(f"- {k}: {v}" for k, v in details.items())
        
        return f"""即将执行操作：{operation}

操作详情：
{details_text}

这个操作可能会产生费用或影响现有资源。请确认是否继续？"""

    def create_progress_prompt(self, operation: str, step: int, total_steps: int, current_step_desc: str) -> str:
        """创建进度提示"""
        progress_percent = int((step / total_steps) * 100)
        
        return f"""正在执行 {operation}... ({step}/{total_steps}, {progress_percent}%)

当前步骤: {current_step_desc}"""

    def create_completion_prompt(self, operation: str, results: Dict[str, Any]) -> str:
        """创建完成提示"""
        return f"""操作 "{operation}" 已完成！

结果摘要：
{self._format_results_summary(results)}

还有什么其他需要帮助的吗？"""

    def _format_results_summary(self, results: Dict[str, Any]) -> str:
        """格式化结果摘要"""
        summary_parts = []
        
        if "cluster_id" in results:
            summary_parts.append(f"集群ID: {results['cluster_id']}")
        
        if "job_id" in results:
            summary_parts.append(f"作业ID: {results['job_id']}")
        
        if "connection_name" in results:
            summary_parts.append(f"连接名称: {results['connection_name']}")
        
        if "status" in results:
            summary_parts.append(f"状态: {results['status']}")
        
        if "execution_time" in results:
            summary_parts.append(f"执行时间: {results['execution_time']}秒")
        
        return "\n".join(f"- {part}" for part in summary_parts) if summary_parts else "操作成功完成"

    def create_help_prompt(self, topic: Optional[str] = None) -> str:
        """创建帮助提示"""
        if topic:
            return f"""关于 {topic} 的帮助信息：

{self._get_topic_help(topic)}

还需要了解其他信息吗？"""
        else:
            return """我可以帮助您进行以下操作：

🏗️ **集群管理**
- 创建集群: "创建一个新的CDM集群"
- 查看集群: "列出所有集群" 或 "查看集群详情"
- 管理集群: "启动/停止/重启集群"

🔗 **连接管理** 
- 创建连接: "创建MySQL连接" 或 "添加OBS连接"
- 查看连接: "列出所有连接"
- 管理连接: "更新/删除连接"

⚡ **作业管理**
- 创建作业: "创建迁移作业"
- 执行作业: "启动/停止作业"
- 监控作业: "查看作业状态"

🚀 **数据迁移**
- 端到端迁移: "从MySQL迁移数据到OBS"
- 批量操作: "批量创建连接"

请告诉我您想要做什么，我会为您提供具体的帮助！"""

    def _get_topic_help(self, topic: str) -> str:
        """获取主题帮助"""
        help_topics = {
            "集群": """CDM集群是执行数据迁移的计算资源。

基本操作：
- 创建集群需要指定名称、模式、版本、规格等参数
- 集群状态包括：创建中(100)、正常(200)、失败(300)、冻结(303)、删除中(800)
- 支持简单模式(simple)和分片模式(sharding)
- 可以随时启动、停止或重启集群""",
            
            "连接": """数据连接用于配置数据源的访问信息。

支持的数据源：
- 关系数据库：MySQL、Oracle、PostgreSQL、SQL Server
- NoSQL：MongoDB、Redis
- 对象存储：OBS、阿里云OSS
- 大数据：HDFS、Hive、HBase
- 消息队列：Kafka
- 文件传输：FTP、SFTP

每种连接需要不同的配置参数，如主机、端口、用户名、密码等。""",
            
            "作业": """数据迁移作业定义了从源到目标的数据传输任务。

作业类型：
- 普通作业(NORMAL_JOB)：标准的数据迁移
- 批量作业(BATCH_JOB)：批量处理多个数据集
- 场景作业(SCENARIO_JOB)：预定义的迁移场景

作业状态：
- BOOTING：启动中
- RUNNING：运行中
- SUCCEEDED：成功完成
- FAILED：执行失败
- KILLED：已终止"""
        }
        
        return help_topics.get(topic, f"抱歉，暂时没有关于 {topic} 的帮助信息。")

    def format_conversation_history(self, messages: List[Dict[str, str]], max_length: int = 10) -> List[Dict[str, str]]:
        """格式化对话历史"""
        # 保留系统消息
        system_messages = [msg for msg in messages if msg["role"] == "system"]
        other_messages = [msg for msg in messages if msg["role"] != "system"]
        
        # 只保留最近的消息
        if len(other_messages) > max_length:
            other_messages = other_messages[-max_length:]
        
        return system_messages + other_messages
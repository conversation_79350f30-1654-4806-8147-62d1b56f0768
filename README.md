# 华为云CDM AI Agent

基于华为云CDM（云数据迁移）API实现的智能管理代理，支持自然语言交互，提供完整的MCP（Model Context Protocol）集成和基于LLM的AI Agent功能。

## 🚀 特性

### 🏗️ 集群管理
- 创建、删除、启动、停止CDM集群
- 支持多种集群规格和版本选择
- 集群状态实时监控
- 自动化集群部署和配置

### 🔗 连接管理
- 支持多种数据源连接类型：MySQL、Oracle、PostgreSQL、SQL Server、MongoDB、Redis、OBS、HDFS、Hive、HBase、FTP、SFTP、Kafka等
- 连接配置管理和验证
- 连接状态监控

### ⚡ 作业管理
- 创建和管理数据迁移作业
- 支持实时作业监控和进度跟踪
- 作业执行历史查询
- 批量作业处理

### 🧠 智能对话
- **大模型驱动**：集成OpenAI GPT、Anthropic Claude等先进LLM
- **Function Calling**：智能函数调用，自动执行CDM操作
- **上下文感知**：多轮对话支持，记住历史操作和状态
- **自然交互**：支持复杂指令和模糊描述的理解

### 🔧 MCP集成
- 完整的MCP服务器实现
- 丰富的工具定义和资源管理
- 提示模板支持
- 与Claude等LLM无缝集成

## 📦 安装

1. **克隆仓库**
```bash
git clone <repository-url>
cd cdm_aiagent
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入华为云相关配置
```

## ⚙️ 配置

在 `.env` 文件中配置以下参数：

```bash
# 华为云基础配置
CDM_HUAWEI_CLOUD_DOMAIN=ap-southeast-1
CDM_HUAWEI_CLOUD_PROJECT_ID=your_project_id_here

# 认证配置
CDM_HUAWEI_CLOUD_USERNAME=your_username
CDM_HUAWEI_CLOUD_PASSWORD=your_password
CDM_HUAWEI_CLOUD_ACCOUNT_NAME=your_account_name

# LLM配置 (必须配置其中一个)
CDM_LLM_PROVIDER=openai
CDM_OPENAI_API_KEY=your_openai_api_key_here
CDM_OPENAI_MODEL=gpt-4-turbo

# 或者使用Claude
# CDM_LLM_PROVIDER=anthropic
# CDM_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 日志配置
CDM_LOG_LEVEL=INFO
CDM_LOG_FILE=logs/cdm_agent.log
```

## 🚀 使用方式

### 1. Web界面模式（推荐）

启动Web服务器：
```bash
python main.py web
```

然后在浏览器中访问 `http://localhost:8000`，使用聊天界面与AI Agent交互。

### 2. MCP服务器模式

启动MCP服务器：
```bash
python main.py mcp
```

可以与支持MCP的客户端（如Claude Desktop）集成使用，提供工具调用能力。

## 💬 对话示例

### 集群管理
```
用户: 列出所有集群
Agent: 🔧 执行操作中... (1/10)
       ✅ cdm_list_clusters: 执行成功
       💬 我为您查询了当前的CDM集群列表，找到以下集群：

       **集群列表 (共2个)：**
       • **my-cdm-cluster** (ID: cluster-123)
         - 状态: 正常 ✅
         - 模式: 简单模式
         - 创建时间: 2024-01-15 10:30:00
       
       • **data-migration-cluster** (ID: cluster-456) 
         - 状态: 创建中 🔄
         - 模式: 分片模式
         - 创建时间: 2024-01-16 14:20:00

       需要查看某个集群的详细信息吗？
```

### 创建集群
```
用户: 创建一个名为test-cluster的集群
Agent: 🔧 执行操作中... (1/10)
       ✅ cdm_get_cluster_versions: 执行成功
       🔧 执行操作中... (2/10)
       ✅ cdm_get_cluster_flavors: 执行成功
       🔧 执行操作中... (3/10)
       ✅ cdm_create_cluster: 执行成功
       💬 我已经为您创建了名为 "test-cluster" 的CDM集群！

       **创建详情：**
       • 集群名称: test-cluster
       • 集群ID: cluster-789
       • 规格: 标准规格 (2核8GB)
       • 版本: 2.9.0
       • 模式: 简单模式
       
       集群正在启动中，预计3-5分钟后可用。您可以说"查看集群 cluster-789 状态"来监控启动进度。
```

### 数据迁移
```
用户: 我需要把生产环境MySQL数据库的用户表迁移到OBS存储桶，大概有100万条记录
Agent: 🔧 执行操作中... (1/10)
       ✅ cdm_list_clusters: 执行成功
       🔧 执行操作中... (2/10) 
       ✅ cdm_create_connection: 执行成功
       🔧 执行操作中... (3/10)
       ✅ cdm_create_connection: 执行成功  
       🔧 执行操作中... (4/10)
       ✅ cdm_create_job: 执行成功
       🔧 执行操作中... (5/10)
       ✅ cdm_start_job: 执行成功
       💬 数据迁移任务已成功启动！

       **迁移配置：**
       • 源端: MySQL数据库 (用户表)
       • 目标: OBS存储桶 
       • 预估数据量: 100万条记录
       • 使用集群: my-cdm-cluster (cluster-123)
       
       **作业信息：**
       • 作业名称: mysql_to_obs_migration
       • 提交ID: sub-789
       • 状态: 运行中 🔄
       
       基于100万条记录，预计迁移时间约15-30分钟。我会持续监控进度，有问题会及时通知您。
```

## 🛠️ API接口

### RESTful API

- `GET /api/clusters` - 获取集群列表
- `GET /api/clusters/{cluster_id}` - 获取集群详情
- `GET /api/intents` - 获取支持的意图列表
- `GET /api/intents/{intent_name}/help` - 获取意图帮助信息
- `GET /api/conversation` - 获取对话历史
- `DELETE /api/conversation` - 清空对话历史

### WebSocket

- `WS /ws` - 实时对话接口

## 🧪 测试

运行测试：
```bash
pytest tests/ -v
```

## 💬 智能对话能力

基于大语言模型，Agent可以理解各种自然语言表达：

### 🗣️ 灵活的表达方式
- **直接指令**: "列出所有集群"、"创建一个集群"
- **描述性请求**: "我想看看现在有哪些CDM集群在运行"
- **复杂场景**: "帮我把MySQL数据库迁移到OBS，大概100万条数据"
- **状态查询**: "刚才创建的作业运行得怎么样？"

### 🎯 智能功能
- **自动推理**: 根据上下文推断缺失参数
- **错误修正**: 自动处理常见错误和重试
- **进度跟踪**: 主动监控长时间运行的任务
- **建议提供**: 基于当前状态提供操作建议

## 🏗️ 架构

```
├── cdm_api/           # CDM API客户端
│   ├── client.py      # API客户端实现
│   ├── models.py      # 数据模型定义
│   ├── auth.py        # 认证模块
│   └── exceptions.py  # 异常定义
├── llm/               # LLM集成模块 ⭐ 新增
│   ├── client.py      # LLM客户端 (OpenAI/Claude)
│   ├── function_calling.py  # Function Calling处理
│   └── prompt_manager.py    # Prompt工程管理
├── mcp_server/        # MCP服务器
│   ├── server.py      # MCP服务器实现
│   └── tools.py       # MCP工具定义
├── agent/             # AI Agent核心 ⭐ 重构
│   ├── core.py        # 基于LLM的Agent核心
│   ├── nlp.py         # 自然语言处理 (保留兼容)
│   └── planner.py     # 任务规划 (保留兼容)
├── tests/             # 测试模块
├── config.py          # 配置管理
└── main.py            # 主应用程序
```

### 🧠 LLM驱动架构

1. **LLM层**：支持OpenAI GPT、Anthropic Claude等多种大模型
2. **Function Calling**：自动解析用户意图并调用相应的CDM API
3. **Context Management**：维护对话历史和操作上下文
4. **Prompt Engineering**：优化的提示词设计，提升理解准确度

## 📝 开发指南

### 添加新的CDM功能

1. 在 `mcp_server/tools.py` 中添加新的MCP工具定义
2. 在 `llm/function_calling.py` 中添加结果格式化逻辑
3. 在 `llm/prompt_manager.py` 中更新系统提示词（如需要）

### 添加新的LLM提供商

1. 在 `llm/client.py` 中实现新的Provider类
2. 在 `LLMClient._create_provider()` 中添加初始化逻辑
3. 在 `config.py` 中添加相应的配置参数

### 自定义Prompt

1. 编辑 `llm/prompt_manager.py` 中的系统提示词
2. 添加特定场景的提示模板
3. 优化函数调用的引导语

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [华为云CDM官方文档](https://support.huaweicloud.com/cdm/)
- [华为云CDM API参考](https://support.huaweicloud.com/api-cdm/)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## 📞 支持

如有问题或建议，请提交 [Issue](../../issues) 或联系项目维护者。
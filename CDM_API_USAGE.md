# CDM API 使用指南

本文档介绍了基于华为云CDM官方API文档实现的MOCK服务和改进后的错误处理功能。

## 目录

1. [MOCK服务](#mock服务)
2. [API模型更新](#api模型更新)
3. [错误处理改进](#错误处理改进)
4. [使用示例](#使用示例)

## MOCK服务

### 概述

基于华为云CDM官方API文档的请求和响应示例，我们构造了一个完整的MOCK服务，用于测试和开发。

### 启动MOCK服务

```bash
# 方式1: 直接运行Python文件
python cdm_api/mock_server.py

# 方式2: 使用批处理文件
run_mock_server.bat
```

MOCK服务将在 `http://localhost:8000` 启动，并提供以下功能：

- FastAPI自动生成的API文档：`http://localhost:8000/docs`
- 完整的CDM API端点模拟
- 基于华为云官方示例的真实响应数据

### 支持的API端点

#### 集群管理API
- `GET /v1.1/{project_id}/clusters` - 查询集群列表
- `GET /v1.1/{project_id}/clusters/{cluster_id}` - 查询集群详情
- `POST /v1.1/{project_id}/clusters` - 创建集群
- `DELETE /v1.1/{project_id}/clusters/{cluster_id}` - 删除集群
- `POST /v1.1/{project_id}/clusters/{cluster_id}/action` - 集群操作（重启/启动/停止）

#### 连接管理API
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/link` - 查询连接列表
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}` - 查询连接详情
- `POST /v1.1/{project_id}/clusters/{cluster_id}/cdm/link` - 创建连接
- `PUT /v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}` - 更新连接
- `DELETE /v1.1/{project_id}/clusters/{cluster_id}/cdm/link/{connection_name}` - 删除连接

#### 作业管理API
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/job` - 查询作业列表
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}` - 查询作业详情
- `POST /v1.1/{project_id}/clusters/{cluster_id}/cdm/job` - 创建作业
- `PUT /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}` - 更新作业
- `DELETE /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}` - 删除作业
- `PUT /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/start` - 启动作业
- `PUT /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/stop` - 停止作业
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/status` - 查询作业状态
- `GET /v1.1/{project_id}/clusters/{cluster_id}/cdm/job/{job_name}/submissions` - 查询作业执行历史

#### 辅助API
- `GET /v1.1/{project_id}/clusters/versions` - 获取支持的集群版本列表
- `GET /v1.1/{project_id}/clusters/flavors` - 获取集群规格列表

### 测试MOCK服务

运行测试脚本验证MOCK服务的功能：

```bash
python test_mock_server.py
```

测试结果将记录在 `test_mock_server.log` 文件中。

## API模型更新

### 主要改进

基于华为云CDM官方API文档的实际请求和响应格式，我们对 `models.py` 进行了重大更新：

#### 1. 集群模型改进
- 更新字段名以匹配API响应（如 `statusDetail`、`created`、`updated`）
- 添加了 `FlavorInfo`、`DatastoreInfo`、`InstanceInfo` 等嵌套模型
- 支持字段别名映射

#### 2. 连接模型改进
- 重构为符合API格式的嵌套结构
- 添加 `ConfigInput`、`ConfigSection`、`LinkConfigValues` 模型
- 支持华为云API的配置格式

#### 3. 作业模型改进
- 更新字段别名以匹配API（如 `from-connector-name`）
- 统一使用 `LinkConfigValues` 配置结构
- 添加作业执行相关的响应模型

#### 4. 新增响应模型
- `CDMErrorResponse` - 标准错误响应
- `ClusterListResponse` - 集群列表响应
- `ConnectionListResponse` - 连接列表响应
- `JobListResponse` - 作业列表响应
- 各种操作的响应模型

### 使用示例

```python
# 创建集群请求
cluster_request = CreateClusterRequest.create_cluster_config(
    name="test-cluster",
    mode="sharding",
    version="*********",
    flavor_id="cdm.medium",
    num_instances=3,
    availability_zone="cn-north-1a",
    vpc_id="vpc-12345678",
    subnet_id="subnet-12345678",
    security_group_id="sg-12345678"
)

# 创建连接请求
connection = Connection(
    name="mysql-conn",
    connector_name="generic-jdbc-connector",
    link_config_values=LinkConfigValues(
        configs=[
            ConfigSection(
                name="linkConfig",
                inputs=[
                    ConfigInput(name="linkConfig.host", value="*************"),
                    ConfigInput(name="linkConfig.port", value="3306"),
                    ConfigInput(name="linkConfig.database", value="testdb")
                ]
            )
        ]
    )
)
```

## 错误处理改进

### 新特性

1. **完整的堆栈跟踪**
   - 每个异常都会记录完整的调用堆栈
   - 原始异常信息保留
   - 支持异常链追踪

2. **详细的错误信息**
   - 记录请求URL、状态码、错误码等上下文信息
   - 支持自定义错误信息字段
   - 提供错误信息的结构化访问

3. **异常装饰器**
   - `@handle_exception` 装饰器自动处理所有异常
   - 统一的异常记录和包装
   - 保持原异常类型的同时添加详细信息

### 新增异常类型

```python
# CDM基础异常
class CDMError(Exception)

# 认证异常
class CDMAuthError(CDMError)  

# API调用异常
class CDMAPIError(CDMError)

# 配置异常
class CDMConfigError(CDMError)

# 超时异常
class CDMTimeoutError(CDMError)

# 连接异常
class CDMConnectionError(CDMError)

# 作业异常
class CDMJobError(CDMError)

# 集群异常
class CDMClusterError(CDMError)
```

### 使用示例

```python
from cdm_api.exceptions import CDMAPIError, handle_exception, log_and_raise

@handle_exception
async def some_api_operation():
    try:
        # 执行API操作
        result = await api_call()
        return result
    except httpx.HTTPError as e:
        # 这会自动记录完整堆栈并重新抛出包装后的异常
        raise CDMAPIError(
            "API调用失败",
            request_url="https://api.example.com/endpoint",
            status_code=500,
            cause=e
        )

# 使用log_and_raise助手函数
def validate_config(config):
    if not config.get('host'):
        log_and_raise(
            CDMConfigError,
            "缺少必需的host配置",
            config_key="host",
            config_value=None
        )
```

### 错误日志示例

```
2024-01-01 12:00:00.000 | ERROR | CDM API Error: Request failed
2024-01-01 12:00:00.000 | ERROR | Request URL: https://cdm.myhuaweicloud.com/v1.1/project/clusters
2024-01-01 12:00:00.000 | ERROR | Status Code: 500
2024-01-01 12:00:00.000 | ERROR | Error Code: CDM.0001
2024-01-01 12:00:00.000 | ERROR | Response Body: {"error_msg": "Internal server error"}
2024-01-01 12:00:00.000 | ERROR | Caused by: HTTPError('Server error')
2024-01-01 12:00:00.000 | ERROR | Full traceback:
Traceback (most recent call last):
  File "client.py", line 67, in _request
    response = await client.request(method, url, **kwargs)
  ...
```

## 使用示例

### 完整的CDM客户端使用示例

```python
import asyncio
from cdm_api.client import CDMClient
from cdm_api.models import CreateClusterRequest, Connection, ConfigInput, ConfigSection, LinkConfigValues
from cdm_api.exceptions import CDMError

async def main():
    # 初始化客户端（可以指向MOCK服务）
    client = CDMClient(
        username="test_user",
        password="test_password", 
        domain_name="test_domain",
        project_id="test_project",
        cdm_endpoint="http://localhost:8000"  # 使用MOCK服务
    )
    
    try:
        # 查询集群列表
        clusters = await client.list_clusters()
        print(f"找到 {clusters.total} 个集群")
        
        # 创建集群
        cluster_request = CreateClusterRequest.create_cluster_config(
            name="my-test-cluster",
            mode="sharding",
            version="*********",
            flavor_id="cdm.medium",
            num_instances=3,
            availability_zone="cn-north-1a",
            vpc_id="vpc-12345678",
            subnet_id="subnet-12345678",
            security_group_id="sg-12345678"
        )
        
        cluster_id = await client.create_cluster(cluster_request)
        print(f"创建的集群ID: {cluster_id}")
        
        # 创建连接
        mysql_connection = Connection(
            name="mysql-source",
            connector_name="generic-jdbc-connector",
            link_config_values=LinkConfigValues(
                configs=[
                    ConfigSection(
                        name="linkConfig",
                        inputs=[
                            ConfigInput(name="linkConfig.databaseType", value="MYSQL"),
                            ConfigInput(name="linkConfig.host", value="*************"),
                            ConfigInput(name="linkConfig.port", value="3306"),
                            ConfigInput(name="linkConfig.database", value="sourcedb"),
                            ConfigInput(name="linkConfig.username", value="root"),
                            ConfigInput(name="linkConfig.password", value="password")
                        ]
                    )
                ]
            )
        )
        
        await client.create_connection(cluster_id, CreateConnectionRequest(links=[mysql_connection]))
        print("MySQL连接创建成功")
        
    except CDMError as e:
        print(f"CDM操作失败: {e.message}")
        print(f"错误详情: {e.get_full_error_info()}")
    except Exception as e:
        print(f"未知错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

### 与真实华为云CDM服务的切换

```python
# 开发/测试环境 - 使用MOCK服务
client = CDMClient(
    cdm_endpoint="http://localhost:8000"
)

# 生产环境 - 使用真实的华为云CDM服务
client = CDMClient(
    username="your_username",
    password="your_password",
    domain_name="your_domain",
    project_id="your_project_id",
    cdm_endpoint="https://cdm.cn-north-1.myhuaweicloud.com"
)
```

## 注意事项

1. **MOCK服务数据**：MOCK服务使用内存存储，重启后数据会丢失
2. **错误日志**：错误日志会自动记录到配置的日志文件中
3. **API兼容性**：模型和MOCK服务严格按照华为云CDM官方API文档设计，确保兼容性
4. **异常处理**：建议在生产环境中适当调整日志级别，避免过多的堆栈信息
5. **测试数据**：MOCK服务提供的是示例数据，实际使用时需要根据需求调整

## 故障排查

1. **MOCK服务启动失败**：
   - 检查8000端口是否被占用
   - 确认依赖包是否安装（fastapi, uvicorn）

2. **API调用失败**：
   - 检查MOCK服务是否正常运行
   - 查看详细的错误日志和堆栈信息
   - 验证请求格式是否符合API规范

3. **模型验证错误**：
   - 检查字段名是否匹配（注意别名配置）
   - 确认必需字段是否提供
   - 查看Pydantic验证错误详情
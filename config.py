import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    from pydantic import BaseSettings, Field
from dotenv import load_dotenv

load_dotenv()

class CDMConfig(BaseSettings):
    """华为CDM服务配置"""
    
    # 华为云基础配置
    huawei_cloud_domain: str = Field(..., description="华为云域名，如 ap-southeast-1")
    huawei_cloud_project_id: str = Field(..., description="华为云项目ID")
    
    # 认证配置
    huawei_cloud_username: str = Field(..., description="华为云用户名")
    huawei_cloud_password: str = Field(..., description="华为云密码")
    huawei_cloud_account_name: str = Field(..., description="华为云账户名")
    
    # CDM服务端点
    iam_endpoint: str = Field(default="", description="IAM服务端点")
    cdm_endpoint: str = Field(default="", description="CDM服务端点")
    
    # Mock服务配置
    mock_cdm_endpoint: str = Field(default="http://localhost:8000", description="Mock CDM服务端点")
    
    # API请求配置
    request_timeout: int = Field(default=30, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    
    # LLM配置
    llm_provider: str = Field(default="ollama", description="LLM提供商 (openai/anthropic/ollama)")
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=None, description="OpenAI API基础URL")
    openai_model: str = Field(default="gpt-4-turbo", description="OpenAI模型名称")
    
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API密钥")
    anthropic_model: str = Field(default="claude-3-sonnet-20240229", description="Anthropic模型名称")
    
    # Ollama配置
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama API基础URL")
    ollama_model: str = Field(default="qwen3:8b", description="Ollama模型名称")
    
    # Agent配置
    max_tokens: int = Field(default=4000, description="最大token数量")
    temperature: float = Field(default=0.1, description="LLM温度参数")
    max_function_calls: int = Field(default=10, description="最大函数调用次数")
    conversation_memory: int = Field(default=20, description="对话记忆条数")
    
    class Config:
        env_prefix = "CDM_"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.iam_endpoint:
            self.iam_endpoint = f"https://iam.{self.huawei_cloud_domain}.myhuaweicloud.com"
        if not self.cdm_endpoint:
            self.cdm_endpoint = f"https://cdm.{self.huawei_cloud_domain}.myhuaweicloud.com"

# 全局配置实例
config = CDMConfig()
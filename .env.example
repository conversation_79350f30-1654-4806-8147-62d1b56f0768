# 华为云CDM AI Agent配置文件示例
# 请复制为.env文件并修改相应配置

# ============================================================================
# 华为云基础配置
# ============================================================================
# 华为云区域，如: ap-southeast-1, cn-north-1等
CDM_HUAWEI_CLOUD_DOMAIN=ap-southeast-1
# 华为云项目ID，在华为云控制台获取
CDM_HUAWEI_CLOUD_PROJECT_ID=your_project_id_here

# ============================================================================
# 认证配置
# ============================================================================
# 华为云用户名
CDM_HUAWEI_CLOUD_USERNAME=your_username_here
# 华为云密码
CDM_HUAWEI_CLOUD_PASSWORD=your_password_here
# 华为云账户名
CDM_HUAWEI_CLOUD_ACCOUNT_NAME=your_account_name_here

# ============================================================================
# CDM服务端点配置
# ============================================================================
# 真实环境端点（可选，会根据domain自动生成）
CDM_IAM_ENDPOINT=
CDM_CDM_ENDPOINT=

# Mock服务端点（用于测试，无需真实账号）
CDM_MOCK_CDM_ENDPOINT=http://localhost:8000

# ============================================================================
# API请求配置
# ============================================================================
# 请求超时时间（秒）
CDM_REQUEST_TIMEOUT=30
# 最大重试次数
CDM_MAX_RETRIES=3

# ============================================================================
# LLM提供商配置
# ============================================================================
# 可选值: ollama (本地), openai (云端), anthropic (云端)
CDM_LLM_PROVIDER=ollama

# OpenAI配置 (如需使用GPT模型)
# CDM_OPENAI_API_KEY=your_openai_api_key_here
# CDM_OPENAI_BASE_URL=https://api.openai.com/v1
CDM_OPENAI_MODEL=gpt-4-turbo

# Anthropic配置 (如需使用Claude模型)
# CDM_ANTHROPIC_API_KEY=your_anthropic_api_key_here
CDM_ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Ollama配置 (默认本地LLM)
CDM_OLLAMA_BASE_URL=http://localhost:11434
CDM_OLLAMA_MODEL=qwen2.5:8b

# ============================================================================
# Agent行为配置
# ============================================================================
# 最大生成token数
CDM_MAX_TOKENS=4000
# LLM创造性参数 (0.0-1.0)
CDM_TEMPERATURE=0.1
# 最大函数调用次数
CDM_MAX_FUNCTION_CALLS=10
# 对话历史记忆条数
CDM_CONVERSATION_MEMORY=20

# ============================================================================
# 日志配置
# ============================================================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
CDM_LOG_LEVEL=INFO
# 日志文件路径（可选）
# CDM_LOG_FILE=logs/cdm_agent.log

# ============================================================================
# 使用说明
# ============================================================================
#
# 1. 测试模式 (推荐开始使用):
#    - 保持CDM_LLM_PROVIDER=ollama
#    - 启动Mock服务: python cdm_api/mock_server.py
#    - 运行交互测试: python interactive_test.py
#
# 2. OpenAI模式:
#    - 设置CDM_LLM_PROVIDER=openai
#    - 取消CDM_OPENAI_API_KEY的注释并填入API密钥
#    - 可选择不同模型: gpt-4-turbo, gpt-3.5-turbo等
#
# 3. Anthropic模式:
#    - 设置CDM_LLM_PROVIDER=anthropic
#    - 取消CDM_ANTHROPIC_API_KEY的注释并填入API密钥
#    - 可选择不同模型: claude-3-sonnet, claude-3-haiku等
#
# 4. 真实CDM环境:
#    - 填写真实的华为云认证信息
#    - 确保华为云账号有CDM服务权限
#    - 注意：真实环境操作会产生费用
#
# ============================================================================
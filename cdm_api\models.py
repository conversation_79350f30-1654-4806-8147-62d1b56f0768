"""
CDM API数据模型定义
"""
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

# 集群相关模型
class ClusterStatus(str, Enum):
    CREATING = "100"
    NORMAL = "200"
    FAILED = "300"
    FROZEN = "303"
    DELETING = "800"

class ClusterMode(str, Enum):
    SHARDING = "sharding"
    SIMPLE = "simple"

class FlavorInfo(BaseModel):
    """集群规格信息"""
    id: str = Field(..., description="规格ID")
    name: str = Field(..., description="规格名称")
    cpu: str = Field(..., description="CPU核数")
    memory: str = Field(..., description="内存大小GB")
    disk: str = Field(..., description="磁盘大小GB")

class DatastoreInfo(BaseModel):
    """数据存储信息"""
    type: str = Field(..., description="数据存储类型")
    version: str = Field(..., description="版本号")

class InstanceInfo(BaseModel):
    """实例信息"""
    id: str = Field(..., description="实例ID")
    name: str = Field(..., description="实例名称")
    role: str = Field(..., description="实例角色")
    status: str = Field(..., description="实例状态")
    flavor: str = Field(..., description="实例规格")

class Cluster(BaseModel):
    id: Optional[str] = Field(None, description="集群ID")
    name: str = Field(..., description="集群名称")
    status: Optional[str] = Field(None, description="集群状态")
    statusDetail: Optional[str] = Field(None, description="集群状态详情")
    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")
    mode: Optional[str] = Field(None, description="集群模式")
    flavor: Optional[FlavorInfo] = Field(None, description="规格信息")
    datastore: Optional[DatastoreInfo] = Field(None, description="数据存储信息")
    instances: Optional[List[InstanceInfo]] = Field(None, description="实例列表")
    endpoint: Optional[str] = Field(None, description="内网端点")
    publicEndpoint: Optional[str] = Field(None, description="公网端点")
    securityGroupId: Optional[str] = Field(None, description="安全组ID")
    subnetId: Optional[str] = Field(None, description="子网ID")
    vpcId: Optional[str] = Field(None, description="VPC ID")
    availabilityZone: Optional[str] = Field(None, description="可用区")
    enterpriseProjectId: Optional[str] = Field(None, description="企业项目ID")

class CreateClusterRequest(BaseModel):
    """创建集群请求"""
    cluster: Dict[str, Any] = Field(..., description="集群配置")
    
    @classmethod
    def create_cluster_config(
        cls,
        name: str,
        mode: str,
        version: str,
        flavor_id: str,
        num_instances: int,
        availability_zone: str,
        vpc_id: str,
        subnet_id: str,
        security_group_id: str,
        public_ip: bool = False,
        enterprise_project_id: str = "0"
    ):
        """创建集群配置"""
        return cls(
            cluster={
                "name": name,
                "mode": mode,
                "datastore": {
                    "type": "cdm",
                    "version": version
                },
                "flavor": flavor_id,
                "instances": num_instances,
                "availabilityZone": availability_zone,
                "vpc": vpc_id,
                "subnet": subnet_id,
                "securityGroup": security_group_id,
                "publicIp": public_ip,
                "enterpriseProjectId": enterprise_project_id
            }
        )

# 连接相关模型
class ConnectionType(str, Enum):
    MYSQL = "generic-jdbc-connector"
    ORACLE = "oracle-connector" 
    POSTGRESQL = "postgresql-connector"
    SQLSERVER = "sqlserver-connector"
    OBS = "obs-connector"
    HDFS = "hdfs-connector"
    HIVE = "hive-connector"
    HBASE = "hbase-connector"
    FTP = "ftp-connector"
    SFTP = "sftp-connector"
    MONGODB = "mongodb-connector"
    REDIS = "redis-connector"
    KAFKA = "kafka-connector"

class ConfigInput(BaseModel):
    """配置输入项"""
    name: str = Field(..., description="配置项名称")
    value: str = Field(..., description="配置项值")

class ConfigSection(BaseModel):
    """配置段"""
    inputs: List[ConfigInput] = Field(..., description="输入配置项")
    name: str = Field(..., description="配置段名称")

class LinkConfigValues(BaseModel):
    """连接配置值"""
    configs: List[ConfigSection] = Field(..., description="配置段列表")

class Connection(BaseModel):
    """连接模型"""
    name: str = Field(..., description="连接名称")
    connector_name: str = Field(..., description="连接器名称", alias="connector-name")
    link_config_values: LinkConfigValues = Field(..., description="连接配置值", alias="link-config-values")
    enabled: Optional[bool] = Field(True, description="是否启用")
    creation_user: Optional[str] = Field(None, description="创建用户", alias="creation-user")
    creation_date: Optional[str] = Field(None, description="创建时间", alias="creation-date")
    update_user: Optional[str] = Field(None, description="更新用户", alias="update-user") 
    update_date: Optional[str] = Field(None, description="更新时间", alias="update-date")
    
    class Config:
        allow_population_by_field_name = True

class CreateConnectionRequest(BaseModel):
    """创建连接请求"""
    links: List[Connection] = Field(..., description="连接列表")

# 作业相关模型
class JobType(str, Enum):
    NORMAL_JOB = "NORMAL_JOB"
    BATCH_JOB = "BATCH_JOB"
    SCENARIO_JOB = "SCENARIO_JOB"

class JobStatus(str, Enum):
    BOOTING = "BOOTING"
    RUNNING = "RUNNING" 
    SUCCEEDED = "SUCCEEDED"
    FAILED = "FAILED"
    KILLED = "KILLED"
    NEVER_EXECUTED = "NEVER_EXECUTED"

class Job(BaseModel):
    """作业模型"""
    name: str = Field(..., description="作业名称")
    type: str = Field("NORMAL_JOB", description="作业类型")
    from_connector_name: str = Field(..., description="源连接器名称", alias="from-connector-name")
    to_connector_name: str = Field(..., description="目标连接器名称", alias="to-connector-name")
    from_link_name: str = Field(..., description="源连接名称", alias="from-link-name")
    to_link_name: str = Field(..., description="目标连接名称", alias="to-link-name")
    from_config_values: LinkConfigValues = Field(..., description="源连接配置", alias="from-config-values")
    to_config_values: LinkConfigValues = Field(..., description="目标连接配置", alias="to-config-values")
    driver_config_values: Optional[LinkConfigValues] = Field(None, description="驱动配置", alias="driver-config-values")
    enabled: Optional[bool] = Field(True, description="是否启用")
    creation_user: Optional[str] = Field(None, description="创建用户", alias="creation-user")
    creation_date: Optional[str] = Field(None, description="创建时间", alias="creation-date")
    update_user: Optional[str] = Field(None, description="更新用户", alias="update-user")
    update_date: Optional[str] = Field(None, description="更新时间", alias="update-date")
    
    class Config:
        allow_population_by_field_name = True

class CreateJobRequest(BaseModel):
    """创建作业请求"""
    jobs: List[Job] = Field(..., description="作业列表")

class JobExecution(BaseModel):
    """作业执行模型"""
    submission_id: Optional[str] = Field(None, description="提交ID", alias="submission-id")
    job_name: Optional[str] = Field(None, description="作业名称", alias="job-name")
    status: Optional[str] = Field(None, description="执行状态")
    creation_date: Optional[str] = Field(None, description="开始时间", alias="creation-date")
    last_update_date: Optional[str] = Field(None, description="最后更新时间", alias="last-update-date")
    counters: Optional[Dict[str, Any]] = Field(None, description="执行计数器")
    progress: Optional[float] = Field(None, description="执行进度")
    
    class Config:
        allow_population_by_field_name = True

class SubmissionResponse(BaseModel):
    """作业提交响应"""
    submission_id: str = Field(..., description="提交ID", alias="submission-id")
    
    class Config:
        allow_population_by_field_name = True

# API响应模型
class APIResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(True, description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误码")
    request_id: Optional[str] = Field(None, description="请求ID")

class CDMErrorResponse(BaseModel):
    """CDM错误响应模型"""
    error_code: str = Field(..., description="错误码")
    error_msg: str = Field(..., description="错误消息")
    request_id: str = Field(..., description="请求ID")

class ListResponse(BaseModel):
    """列表响应模型"""
    total: int = Field(..., description="总数")
    items: List[Any] = Field(..., description="数据项")
    page: Optional[int] = Field(None, description="当前页")
    size: Optional[int] = Field(None, description="页大小")

class ClusterListResponse(BaseModel):
    """集群列表响应"""
    clusters: List[Cluster] = Field(..., description="集群列表")
    total: int = Field(..., description="总数")

class ClusterResponse(BaseModel):
    """集群详情响应"""
    cluster: Cluster = Field(..., description="集群信息")

class ConnectionListResponse(BaseModel):
    """连接列表响应"""
    links: List[Connection] = Field(..., description="连接列表")

class ConnectionResponse(BaseModel):
    """连接详情响应"""
    link: Connection = Field(..., description="连接信息")

class JobListResponse(BaseModel):
    """作业列表响应"""
    jobs: List[Job] = Field(..., description="作业列表")

class JobResponse(BaseModel):
    """作业详情响应"""
    job: Job = Field(..., description="作业信息")

class JobExecutionListResponse(BaseModel):
    """作业执行历史响应"""
    submissions: List[JobExecution] = Field(..., description="执行列表")

class CreateClusterResponse(BaseModel):
    """创建集群响应"""
    id: str = Field(..., description="集群ID")

class ClusterActionResponse(BaseModel):
    """集群操作响应"""
    jobId: str = Field(..., description="操作任务ID")
    message: Optional[str] = Field(None, description="响应消息")

class JobStatusResponse(BaseModel):
    """作业状态响应"""
    status: str = Field(..., description="作业状态")
    progress: float = Field(0.0, description="执行进度")
    message: Optional[str] = Field(None, description="状态消息")

class FlavorListResponse(BaseModel):
    """规格列表响应"""
    flavors: List[FlavorInfo] = Field(..., description="规格列表")

class VersionListResponse(BaseModel):
    """版本列表响应"""
    versions: List[str] = Field(..., description="版本列表")
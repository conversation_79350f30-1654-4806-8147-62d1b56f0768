#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用假token访问mock CDM API的集成测试
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cdm_api.client import CDMClient
from cdm_api.tools import CDMTools
from loguru import logger


async def test_mock_integration():
    """测试mock模式的集成功能"""
    print("=" * 60)
    print("CDM Mock Integration Test")
    print("=" * 60)
    
    # 1. 测试CDM客户端的mock模式
    print("\n1. 测试CDM客户端mock模式...")
    try:
        # 创建mock模式客户端
        client = CDMClient(mock_mode=True, cdm_endpoint="http://localhost:8000")
        print("[OK] CDM客户端创建成功（mock模式）")
        
        # 测试获取集群列表
        print("正在测试获取集群列表...")
        clusters = await client.list_clusters()
        print(f"[OK] 获取集群列表成功，共 {clusters.total} 个集群")
        for cluster in clusters.items[:2]:  # 只显示前2个
            print(f"  - 集群: {cluster.name} (ID: {cluster.id}, 状态: {cluster.status})")
            
    except Exception as e:
        print(f"[ERROR] CDM客户端测试失败: {e}")
        return False
    
    # 2. 测试CDM工具类
    print("\n2. 测试CDM工具类...")
    try:
        cdm_tools = CDMTools(mock_mode=True)
        print("[OK] CDM工具类创建成功（mock模式）")
        
        # 测试获取集群列表
        result = await cdm_tools.execute_function("list_clusters", {"page": 1, "size": 5})
        if result.get("success"):
            print(f"[OK] 工具函数执行成功: {result['message']}")
            print(f"  数据: 共 {result['data']['total']} 个集群")
        else:
            print(f"[ERROR] 工具函数执行失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"[ERROR] CDM工具类测试失败: {e}")
        return False
    
    # 3. 测试获取集群详情
    print("\n3. 测试获取集群详情...")
    try:
        cluster_id = "cluster-001" 
        result = await cdm_tools.execute_function("get_cluster", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 获取集群详情成功: {result['message']}")
            cluster_data = result['data']
            print(f"  集群名称: {cluster_data['name']}")
            print(f"  集群状态: {cluster_data['status']}")
            print(f"  集群模式: {cluster_data['mode']}")
        else:
            print(f"[ERROR] 获取集群详情失败: {result.get('error')}")
            
    except Exception as e:
        print(f"[ERROR] 获取集群详情测试失败: {e}")
    
    # 4. 测试获取连接列表
    print("\n4. 测试获取连接列表...")
    try:
        cluster_id = "cluster-001"
        result = await cdm_tools.execute_function("list_connections", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 获取连接列表成功: {result['message']}")
            connections = result['data']['connections']
            for conn in connections[:2]:  # 只显示前2个
                print(f"  - 连接: {conn['name']}")
        else:
            print(f"[ERROR] 获取连接列表失败: {result.get('error')}")
            
    except Exception as e:
        print(f"[ERROR] 获取连接列表测试失败: {e}")
    
    # 5. 测试获取作业列表
    print("\n5. 测试获取作业列表...")
    try:
        cluster_id = "cluster-001"
        result = await cdm_tools.execute_function("list_jobs", {"cluster_id": cluster_id})
        if result.get("success"):
            print(f"[OK] 获取作业列表成功: {result['message']}")
            jobs = result['data']['jobs']
            for job in jobs[:2]:  # 只显示前2个
                print(f"  - 作业: {job['name']} (类型: {job['type']})")
        else:
            print(f"[ERROR] 获取作业列表失败: {result.get('error')}")
            
    except Exception as e:
        print(f"[ERROR] 获取作业列表测试失败: {e}")
    
    # 6. 测试获取集群版本列表
    print("\n6. 测试获取集群版本...")
    try:
        result = await cdm_tools.execute_function("get_cluster_versions", {})
        if result.get("success"):
            print(f"[OK] 获取版本列表成功: {result['message']}")
            versions = result['data']['versions']
            print(f"  支持的版本: {', '.join(versions)}")
        else:
            print(f"[ERROR] 获取版本列表失败: {result.get('error')}")
            
    except Exception as e:
        print(f"[ERROR] 获取版本列表测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！Mock API集成正常工作")
    print("您现在可以使用交互式测试而无需真实的华为云凭据")
    print("=" * 60)
    
    return True


async def main():
    """主函数"""
    print("启动Mock CDM API集成测试...")
    print("注意: 请确保mock服务器正在运行 (python -m cdm_api.mock_server)")
    print()
    
    # 检查mock服务是否运行
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000", timeout=5)
            if response.status_code == 200:
                print("[OK] Mock服务器正在运行")
            else:
                print("[ERROR] Mock服务器响应异常")
                return
    except Exception as e:
        print(f"[ERROR] 无法连接到Mock服务器: {e}")
        print("请先启动mock服务器: python -m cdm_api.mock_server")
        return
    
    # 运行测试
    success = await test_mock_integration()
    
    if success:
        print("\n[SUCCESS] 所有测试通过！现在可以开始使用交互式测试了。")
        print("运行命令: python interactive_test.py")
    else:
        print("\n[FAILED] 部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
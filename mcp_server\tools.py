"""
CDM MCP工具定义
"""
import json
from typing import Any, Dict, List, Optional

import mcp.types as types
from loguru import logger

from cdm_api.client import CDMClient
from cdm_api.models import (
    CreateClusterRequest, CreateConnectionRequest, CreateJobRequest,
    Connection, Job, ConnectionType, JobType, ClusterMode
)
from cdm_api.exceptions import CDMAPIError, CDMAuthError, CDMTimeoutError


class CDMTools:
    """CDM工具类"""
    
    def __init__(self, cdm_client: CDMClient):
        self.cdm_client = cdm_client
        
    def get_tool_definitions(self) -> List[types.Tool]:
        """获取工具定义列表"""
        return [
            # 集群管理工具
            types.Tool(
                name="cdm_list_clusters",
                description="查询CDM集群列表",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "page": {
                            "type": "integer",
                            "description": "页码，默认为1",
                            "default": 1
                        },
                        "size": {
                            "type": "integer", 
                            "description": "每页大小，默认为100",
                            "default": 100
                        }
                    }
                }
            ),
            types.Tool(
                name="cdm_get_cluster",
                description="查询CDM集群详情",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_create_cluster",
                description="创建CDM集群",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "集群名称"
                        },
                        "mode": {
                            "type": "string",
                            "enum": ["sharding", "simple"],
                            "description": "集群模式"
                        },
                        "version": {
                            "type": "string",
                            "description": "集群版本"
                        },
                        "flavor_id": {
                            "type": "string", 
                            "description": "规格ID"
                        },
                        "num_instances": {
                            "type": "integer",
                            "description": "节点数量"
                        },
                        "availability_zone": {
                            "type": "string",
                            "description": "可用区"
                        },
                        "vpc_id": {
                            "type": "string",
                            "description": "VPC ID"
                        },
                        "subnet_id": {
                            "type": "string",
                            "description": "子网ID"
                        },
                        "security_group_id": {
                            "type": "string",
                            "description": "安全组ID"
                        },
                        "public_ip": {
                            "type": "boolean",
                            "description": "是否绑定公网IP",
                            "default": False
                        },
                        "enterprise_project_id": {
                            "type": "string",
                            "description": "企业项目ID（可选）"
                        }
                    },
                    "required": [
                        "name", "mode", "version", "flavor_id", "num_instances",
                        "availability_zone", "vpc_id", "subnet_id", "security_group_id"
                    ]
                }
            ),
            types.Tool(
                name="cdm_delete_cluster",
                description="删除CDM集群",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_start_cluster",
                description="启动CDM集群",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_stop_cluster",
                description="停止CDM集群",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_restart_cluster",
                description="重启CDM集群",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            
            # 连接管理工具
            types.Tool(
                name="cdm_list_connections",
                description="查询CDM连接列表",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_get_connection",
                description="查询CDM连接详情",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        }
                    },
                    "required": ["cluster_id", "connection_name"]
                }
            ),
            types.Tool(
                name="cdm_create_connection",
                description="创建CDM连接",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "name": {
                            "type": "string",
                            "description": "连接名称"
                        },
                        "connector_name": {
                            "type": "string",
                            "enum": [
                                "generic-jdbc-connector", "oracle-connector", "postgresql-connector",
                                "sqlserver-connector", "obs-connector", "hdfs-connector", 
                                "hive-connector", "hbase-connector", "ftp-connector", "sftp-connector",
                                "mongodb-connector", "redis-connector", "kafka-connector"
                            ],
                            "description": "连接器类型"
                        },
                        "config": {
                            "type": "object",
                            "description": "连接配置参数"
                        }
                    },
                    "required": ["cluster_id", "name", "connector_name", "config"]
                }
            ),
            types.Tool(
                name="cdm_update_connection",
                description="更新CDM连接",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        },
                        "config": {
                            "type": "object",
                            "description": "更新的连接配置"
                        }
                    },
                    "required": ["cluster_id", "connection_name", "config"]
                }
            ),
            types.Tool(
                name="cdm_delete_connection",
                description="删除CDM连接",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        }
                    },
                    "required": ["cluster_id", "connection_name"]
                }
            ),
            
            # 作业管理工具
            types.Tool(
                name="cdm_list_jobs",
                description="查询CDM作业列表",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            ),
            types.Tool(
                name="cdm_get_job",
                description="查询CDM作业详情",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_create_job",
                description="创建CDM作业",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "name": {
                            "type": "string",
                            "description": "作业名称"
                        },
                        "type": {
                            "type": "string",
                            "enum": ["NORMAL_JOB", "BATCH_JOB", "SCENARIO_JOB"],
                            "description": "作业类型",
                            "default": "NORMAL_JOB"
                        },
                        "from_connector_name": {
                            "type": "string",
                            "description": "源连接器名称"
                        },
                        "to_connector_name": {
                            "type": "string",
                            "description": "目标连接器名称"
                        },
                        "from_config_values": {
                            "type": "object",
                            "description": "源连接配置"
                        },
                        "to_config_values": {
                            "type": "object",
                            "description": "目标连接配置"
                        },
                        "driver_config_values": {
                            "type": "object",
                            "description": "驱动配置（可选）"
                        }
                    },
                    "required": [
                        "cluster_id", "name", "from_connector_name", "to_connector_name",
                        "from_config_values", "to_config_values"
                    ]
                }
            ),
            types.Tool(
                name="cdm_update_job",
                description="更新CDM作业",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        },
                        "from_config_values": {
                            "type": "object",
                            "description": "源连接配置"
                        },
                        "to_config_values": {
                            "type": "object",
                            "description": "目标连接配置"
                        },
                        "driver_config_values": {
                            "type": "object",
                            "description": "驱动配置（可选）"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_delete_job",
                description="删除CDM作业",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_start_job",
                description="启动CDM作业",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_stop_job",
                description="停止CDM作业",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_get_job_status",
                description="查询CDM作业状态",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            types.Tool(
                name="cdm_get_job_executions",
                description="查询CDM作业执行历史",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            ),
            
            # 辅助工具
            types.Tool(
                name="cdm_get_cluster_versions",
                description="获取支持的CDM集群版本列表",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            types.Tool(
                name="cdm_get_cluster_flavors",
                description="获取CDM集群规格列表",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            types.Tool(
                name="cdm_wait_cluster_ready",
                description="等待集群就绪",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "timeout": {
                            "type": "integer",
                            "description": "超时时间（秒），默认1800秒",
                            "default": 1800
                        }
                    },
                    "required": ["cluster_id"]
                }
            )
        ]
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
        """调用工具"""
        try:
            if name == "cdm_list_clusters":
                return await self._list_clusters(**arguments)
            elif name == "cdm_get_cluster":
                return await self._get_cluster(**arguments)
            elif name == "cdm_create_cluster":
                return await self._create_cluster(**arguments)
            elif name == "cdm_delete_cluster":
                return await self._delete_cluster(**arguments)
            elif name == "cdm_start_cluster":
                return await self._start_cluster(**arguments)
            elif name == "cdm_stop_cluster":
                return await self._stop_cluster(**arguments)
            elif name == "cdm_restart_cluster":
                return await self._restart_cluster(**arguments)
                
            elif name == "cdm_list_connections":
                return await self._list_connections(**arguments)
            elif name == "cdm_get_connection":
                return await self._get_connection(**arguments)
            elif name == "cdm_create_connection":
                return await self._create_connection(**arguments)
            elif name == "cdm_update_connection":
                return await self._update_connection(**arguments)
            elif name == "cdm_delete_connection":
                return await self._delete_connection(**arguments)
                
            elif name == "cdm_list_jobs":
                return await self._list_jobs(**arguments)
            elif name == "cdm_get_job":
                return await self._get_job(**arguments)
            elif name == "cdm_create_job":
                return await self._create_job(**arguments)
            elif name == "cdm_update_job":
                return await self._update_job(**arguments)
            elif name == "cdm_delete_job":
                return await self._delete_job(**arguments)
            elif name == "cdm_start_job":
                return await self._start_job(**arguments)
            elif name == "cdm_stop_job":
                return await self._stop_job(**arguments)
            elif name == "cdm_get_job_status":
                return await self._get_job_status(**arguments)
            elif name == "cdm_get_job_executions":
                return await self._get_job_executions(**arguments)
                
            elif name == "cdm_get_cluster_versions":
                return await self._get_cluster_versions(**arguments)
            elif name == "cdm_get_cluster_flavors":
                return await self._get_cluster_flavors(**arguments)
            elif name == "cdm_wait_cluster_ready":
                return await self._wait_cluster_ready(**arguments)
            else:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]
                
        except CDMAPIError as e:
            error_msg = f"CDM API错误: {e.message}"
            if e.error_code:
                error_msg += f" (错误码: {e.error_code})"
            logger.error(error_msg)
            return [types.TextContent(type="text", text=error_msg)]
            
        except CDMAuthError as e:
            error_msg = f"CDM认证错误: {str(e)}"
            logger.error(error_msg)
            return [types.TextContent(type="text", text=error_msg)]
            
        except CDMTimeoutError as e:
            error_msg = f"CDM请求超时: {str(e)}"
            logger.error(error_msg)
            return [types.TextContent(type="text", text=error_msg)]
            
        except Exception as e:
            error_msg = f"工具执行异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return [types.TextContent(type="text", text=error_msg)]
            
    # 集群管理工具实现
    async def _list_clusters(self, page: int = 1, size: int = 100) -> List[types.TextContent]:
        result = await self.cdm_client.list_clusters(page, size)
        clusters_info = []
        for cluster in result.items:
            clusters_info.append({
                "id": cluster.id,
                "name": cluster.name,
                "status": cluster.status,
                "status_detail": cluster.status_detail,
                "mode": cluster.mode,
                "version": cluster.version,
                "created_at": cluster.created_at.isoformat() if cluster.created_at else None
            })
        
        response = {
            "total": result.total,
            "page": result.page,
            "size": result.size,
            "clusters": clusters_info
        }
        
        return [types.TextContent(type="text", text=json.dumps(response, ensure_ascii=False, indent=2))]
    
    async def _get_cluster(self, cluster_id: str) -> List[types.TextContent]:
        cluster = await self.cdm_client.get_cluster(cluster_id)
        result = cluster.dict()
        if result.get('created_at'):
            result['created_at'] = cluster.created_at.isoformat()
        if result.get('updated_at'):
            result['updated_at'] = cluster.updated_at.isoformat()
            
        return [types.TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    async def _create_cluster(self, **kwargs) -> List[types.TextContent]:
        # 移除不属于CreateClusterRequest的参数
        cluster_params = {k: v for k, v in kwargs.items() if k != "cluster_id"}
        request = CreateClusterRequest(**cluster_params)
        cluster_id = await self.cdm_client.create_cluster(request)
        
        return [types.TextContent(type="text", text=f"集群创建成功，集群ID: {cluster_id}")]
    
    async def _delete_cluster(self, cluster_id: str) -> List[types.TextContent]:
        await self.cdm_client.delete_cluster(cluster_id)
        return [types.TextContent(type="text", text=f"集群 {cluster_id} 删除成功")]
    
    async def _start_cluster(self, cluster_id: str) -> List[types.TextContent]:
        await self.cdm_client.start_cluster(cluster_id)
        return [types.TextContent(type="text", text=f"集群 {cluster_id} 启动成功")]
    
    async def _stop_cluster(self, cluster_id: str) -> List[types.TextContent]:
        await self.cdm_client.stop_cluster(cluster_id)
        return [types.TextContent(type="text", text=f"集群 {cluster_id} 停止成功")]
    
    async def _restart_cluster(self, cluster_id: str) -> List[types.TextContent]:
        await self.cdm_client.restart_cluster(cluster_id)
        return [types.TextContent(type="text", text=f"集群 {cluster_id} 重启成功")]
    
    # 连接管理工具实现
    async def _list_connections(self, cluster_id: str) -> List[types.TextContent]:
        connections = await self.cdm_client.list_connections(cluster_id)
        connections_info = []
        for conn in connections:
            connections_info.append({
                "name": conn.name,
                "connector_name": conn.connector_name,
                "enabled": conn.enabled,
                "creation_user": conn.creation_user,
                "creation_date": conn.creation_date.isoformat() if conn.creation_date else None
            })
            
        return [types.TextContent(type="text", text=json.dumps(connections_info, ensure_ascii=False, indent=2))]
    
    async def _get_connection(self, cluster_id: str, connection_name: str) -> List[types.TextContent]:
        connection = await self.cdm_client.get_connection(cluster_id, connection_name)
        result = connection.dict()
        if result.get('creation_date'):
            result['creation_date'] = connection.creation_date.isoformat()
        if result.get('update_date'):
            result['update_date'] = connection.update_date.isoformat()
            
        return [types.TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    async def _create_connection(self, cluster_id: str, name: str, connector_name: str, 
                               config: Dict[str, Any]) -> List[types.TextContent]:
        connection = Connection(name=name, connector_name=connector_name, config=config)
        request = CreateConnectionRequest(connections=[connection])
        await self.cdm_client.create_connection(cluster_id, request)
        
        return [types.TextContent(type="text", text=f"连接 {name} 创建成功")]
    
    async def _update_connection(self, cluster_id: str, connection_name: str, 
                               config: Dict[str, Any]) -> List[types.TextContent]:
        # 先获取现有连接信息
        existing_conn = await self.cdm_client.get_connection(cluster_id, connection_name)
        existing_conn.config.update(config)
        
        await self.cdm_client.update_connection(cluster_id, connection_name, existing_conn)
        return [types.TextContent(type="text", text=f"连接 {connection_name} 更新成功")]
    
    async def _delete_connection(self, cluster_id: str, connection_name: str) -> List[types.TextContent]:
        await self.cdm_client.delete_connection(cluster_id, connection_name)
        return [types.TextContent(type="text", text=f"连接 {connection_name} 删除成功")]
    
    # 作业管理工具实现
    async def _list_jobs(self, cluster_id: str) -> List[types.TextContent]:
        jobs = await self.cdm_client.list_jobs(cluster_id)
        jobs_info = []
        for job in jobs:
            jobs_info.append({
                "name": job.name,
                "type": job.type,
                "from_connector_name": job.from_connector_name,
                "to_connector_name": job.to_connector_name,
                "enabled": job.enabled,
                "creation_user": job.creation_user,
                "creation_date": job.creation_date.isoformat() if job.creation_date else None
            })
            
        return [types.TextContent(type="text", text=json.dumps(jobs_info, ensure_ascii=False, indent=2))]
    
    async def _get_job(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        job = await self.cdm_client.get_job(cluster_id, job_name)
        result = job.dict()
        if result.get('creation_date'):
            result['creation_date'] = job.creation_date.isoformat()
        if result.get('update_date'):
            result['update_date'] = job.update_date.isoformat()
            
        return [types.TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    async def _create_job(self, cluster_id: str, name: str, from_connector_name: str,
                         to_connector_name: str, from_config_values: Dict[str, Any],
                         to_config_values: Dict[str, Any], type: str = "NORMAL_JOB",
                         driver_config_values: Dict[str, Any] = None) -> List[types.TextContent]:
        job = Job(
            name=name,
            type=type,
            from_connector_name=from_connector_name,
            to_connector_name=to_connector_name,
            from_config_values=from_config_values,
            to_config_values=to_config_values,
            driver_config_values=driver_config_values or {}
        )
        request = CreateJobRequest(jobs=[job])
        await self.cdm_client.create_job(cluster_id, request)
        
        return [types.TextContent(type="text", text=f"作业 {name} 创建成功")]
    
    async def _update_job(self, cluster_id: str, job_name: str, 
                         from_config_values: Dict[str, Any] = None,
                         to_config_values: Dict[str, Any] = None,
                         driver_config_values: Dict[str, Any] = None) -> List[types.TextContent]:
        # 先获取现有作业信息
        existing_job = await self.cdm_client.get_job(cluster_id, job_name)
        
        if from_config_values:
            existing_job.from_config_values.update(from_config_values)
        if to_config_values:
            existing_job.to_config_values.update(to_config_values)
        if driver_config_values:
            existing_job.driver_config_values = existing_job.driver_config_values or {}
            existing_job.driver_config_values.update(driver_config_values)
            
        await self.cdm_client.update_job(cluster_id, job_name, existing_job)
        return [types.TextContent(type="text", text=f"作业 {job_name} 更新成功")]
    
    async def _delete_job(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        await self.cdm_client.delete_job(cluster_id, job_name)
        return [types.TextContent(type="text", text=f"作业 {job_name} 删除成功")]
    
    async def _start_job(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        submission = await self.cdm_client.start_job(cluster_id, job_name)
        return [types.TextContent(type="text", text=f"作业 {job_name} 启动成功，提交ID: {submission.submission_id}")]
    
    async def _stop_job(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        await self.cdm_client.stop_job(cluster_id, job_name)
        return [types.TextContent(type="text", text=f"作业 {job_name} 停止成功")]
    
    async def _get_job_status(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        status = await self.cdm_client.get_job_status(cluster_id, job_name)
        return [types.TextContent(type="text", text=json.dumps(status, ensure_ascii=False, indent=2))]
    
    async def _get_job_executions(self, cluster_id: str, job_name: str) -> List[types.TextContent]:
        executions = await self.cdm_client.get_job_executions(cluster_id, job_name)
        executions_info = []
        for exec in executions:
            exec_info = {
                "id": exec.id,
                "job_id": exec.job_id,
                "status": exec.status,
                "progress": exec.progress,
                "creation_date": exec.creation_date.isoformat() if exec.creation_date else None,
                "last_update_date": exec.last_update_date.isoformat() if exec.last_update_date else None
            }
            if exec.counters:
                exec_info["counters"] = exec.counters
            executions_info.append(exec_info)
            
        return [types.TextContent(type="text", text=json.dumps(executions_info, ensure_ascii=False, indent=2))]
    
    # 辅助工具实现
    async def _get_cluster_versions(self) -> List[types.TextContent]:
        versions = await self.cdm_client.get_cluster_versions()
        return [types.TextContent(type="text", text=json.dumps(versions, ensure_ascii=False, indent=2))]
    
    async def _get_cluster_flavors(self) -> List[types.TextContent]:
        flavors = await self.cdm_client.get_cluster_flavors()
        return [types.TextContent(type="text", text=json.dumps(flavors, ensure_ascii=False, indent=2))]
    
    async def _wait_cluster_ready(self, cluster_id: str, timeout: int = 1800) -> List[types.TextContent]:
        success = await self.cdm_client.wait_cluster_ready(cluster_id, timeout)
        if success:
            return [types.TextContent(type="text", text=f"集群 {cluster_id} 已就绪")]
        else:
            return [types.TextContent(type="text", text=f"等待集群 {cluster_id} 就绪超时或失败")]
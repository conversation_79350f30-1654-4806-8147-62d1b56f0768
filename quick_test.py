# -*- coding: utf-8 -*-
"""
快速测试CDM AI Agent与Ollama集成
"""
import asyncio
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm.client import LLMClient
from cdm_api.tools import CDMTools

async def test_integration():
    """测试CDM Agent与Ollama集成"""
    print("Testing CDM AI Agent with Ollama...")
    
    try:
        # 初始化组件
        llm_client = LLMClient()
        cdm_tools = CDMTools(mock_mode=True)
        
        # 测试消息
        messages = [
            {
                "role": "system", 
                "content": "你是华为云CDM服务的智能助手。用户询问CDM相关问题时，使用提供的工具函数。"
            },
            {"role": "user", "content": "请查询当前有哪些CDM集群"}
        ]
        
        # 获取函数定义
        functions = cdm_tools.get_function_definitions()
        print(f"Available functions: {[f['name'] for f in functions]}")
        
        # 调用LLM
        print("Calling LLM with CDM functions...")
        response = await llm_client.chat(messages, functions=functions)
        
        print(f"LLM Response: {response}")
        
        # 如果有工具调用
        if 'tool_calls' in response:
            print("Tool calls detected!")
            for tool_call in response['tool_calls']:
                func_name = tool_call['function']['name']
                func_args_str = tool_call['function']['arguments']
                print(f"Function: {func_name}, Args: {func_args_str}")
                
                # 执行函数
                import json
                func_args = json.loads(func_args_str)
                result = await cdm_tools.execute_function(func_name, func_args)
                print(f"Function result: {result}")
            
            return True
        else:
            print("No tool calls detected")
            return False
            
    except Exception as e:
        print(f"Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("CDM AI Agent + Ollama Integration Test")
    print("=" * 50)
    
    result = await test_integration()
    
    print("\n" + "=" * 50)
    print(f"Integration Test: {'PASSED' if result else 'FAILED'}")
    
    if result:
        print("\nSuccess! CDM AI Agent is working with Ollama!")
        print("You can now run the full interactive test:")
        print("python test_ollama_integration.py")
    else:
        print("\nIntegration test failed. Please check the logs.")

if __name__ == "__main__":
    asyncio.run(main())
# CDM AI Agent 配置指南

## 概述

CDM AI Agent使用环境变量进行配置管理，所有配置项都以`CDM_`前缀开头。配置优先级：环境变量 > .env文件 > 默认值。

## 配置文件

- **`.env`** - 当前使用的配置文件
- **`.env.example`** - 配置示例文件，包含所有可用选项和使用说明

## 配置分类

### 🏢 华为云基础配置

| 配置项 | 说明 | 示例 | 必需 |
|-------|------|------|------|
| `CDM_HUAWEI_CLOUD_DOMAIN` | 华为云区域域名 | ap-southeast-1 | ✅ |
| `CDM_HUAWEI_CLOUD_PROJECT_ID` | 华为云项目ID | abc123def456 | ✅ |
| `CDM_HUAWEI_CLOUD_USERNAME` | 华为云用户名 | your_username | ✅ |
| `CDM_HUAWEI_CLOUD_PASSWORD` | 华为云密码 | your_password | ✅ |
| `CDM_HUAWEI_CLOUD_ACCOUNT_NAME` | 华为云账户名 | your_account | ✅ |

### 🔗 服务端点配置

| 配置项 | 说明 | 默认值 | 必需 |
|-------|------|--------|------|
| `CDM_IAM_ENDPOINT` | IAM服务端点 | 自动生成 | ❌ |
| `CDM_CDM_ENDPOINT` | CDM服务端点 | 自动生成 | ❌ |
| `CDM_MOCK_CDM_ENDPOINT` | Mock服务端点 | http://localhost:8000 | ❌ |

### ⚙️ API请求配置

| 配置项 | 说明 | 默认值 | 范围 |
|-------|------|--------|------|
| `CDM_REQUEST_TIMEOUT` | 请求超时时间(秒) | 30 | 1-300 |
| `CDM_MAX_RETRIES` | 最大重试次数 | 3 | 0-10 |

### 🤖 LLM提供商配置

#### 通用配置
| 配置项 | 说明 | 可选值 | 默认值 |
|-------|------|--------|--------|
| `CDM_LLM_PROVIDER` | LLM提供商 | ollama, openai, anthropic | ollama |

#### OpenAI配置
| 配置项 | 说明 | 默认值 | 必需 |
|-------|------|--------|------|
| `CDM_OPENAI_API_KEY` | OpenAI API密钥 | - | ✅* |
| `CDM_OPENAI_BASE_URL` | OpenAI API地址 | https://api.openai.com/v1 | ❌ |
| `CDM_OPENAI_MODEL` | OpenAI模型 | gpt-4-turbo | ❌ |

*仅当使用OpenAI时必需

#### Anthropic配置
| 配置项 | 说明 | 默认值 | 必需 |
|-------|------|--------|------|
| `CDM_ANTHROPIC_API_KEY` | Anthropic API密钥 | - | ✅* |
| `CDM_ANTHROPIC_MODEL` | Claude模型 | claude-3-sonnet-******** | ❌ |

*仅当使用Anthropic时必需

#### Ollama配置
| 配置项 | 说明 | 默认值 | 必需 |
|-------|------|--------|------|
| `CDM_OLLAMA_BASE_URL` | Ollama服务地址 | http://localhost:11434 | ❌ |
| `CDM_OLLAMA_MODEL` | Ollama模型 | qwen2.5:8b | ❌ |

### 🎯 Agent行为配置

| 配置项 | 说明 | 默认值 | 范围 |
|-------|------|--------|------|
| `CDM_MAX_TOKENS` | 最大生成token数 | 4000 | 100-32000 |
| `CDM_TEMPERATURE` | LLM创造性参数 | 0.1 | 0.0-1.0 |
| `CDM_MAX_FUNCTION_CALLS` | 最大函数调用次数 | 10 | 1-50 |
| `CDM_CONVERSATION_MEMORY` | 对话记忆条数 | 20 | 1-100 |

### 📝 日志配置

| 配置项 | 说明 | 默认值 | 可选值 |
|-------|------|--------|--------|
| `CDM_LOG_LEVEL` | 日志级别 | INFO | DEBUG, INFO, WARNING, ERROR |
| `CDM_LOG_FILE` | 日志文件路径 | 无(控制台输出) | 文件路径 |

## 使用场景

### 🧪 测试模式（推荐新用户）

```bash
# .env配置
CDM_LLM_PROVIDER=ollama
CDM_HUAWEI_CLOUD_DOMAIN=ap-southeast-1
CDM_HUAWEI_CLOUD_PROJECT_ID=test_project_id
CDM_HUAWEI_CLOUD_USERNAME=test_user
CDM_HUAWEI_CLOUD_PASSWORD=test_password
CDM_HUAWEI_CLOUD_ACCOUNT_NAME=test_account

# 启动步骤
1. python cdm_api/mock_server.py  # 启动Mock服务
2. python interactive_test.py      # 开始交互测试
```

### 🌐 云端LLM模式

#### OpenAI配置
```bash
# .env配置
CDM_LLM_PROVIDER=openai
CDM_OPENAI_API_KEY=sk-your_api_key_here
CDM_OPENAI_MODEL=gpt-4-turbo

# 其他华为云配置保持不变
```

#### Anthropic配置
```bash
# .env配置
CDM_LLM_PROVIDER=anthropic
CDM_ANTHROPIC_API_KEY=your_anthropic_key_here
CDM_ANTHROPIC_MODEL=claude-3-sonnet-********
```

### 🏭 生产环境模式

```bash
# .env配置
CDM_LLM_PROVIDER=openai  # 或其他稳定提供商
CDM_HUAWEI_CLOUD_DOMAIN=cn-north-1  # 实际区域
CDM_HUAWEI_CLOUD_PROJECT_ID=real_project_id
CDM_HUAWEI_CLOUD_USERNAME=real_username
CDM_HUAWEI_CLOUD_PASSWORD=real_password
CDM_HUAWEI_CLOUD_ACCOUNT_NAME=real_account
CDM_LOG_LEVEL=WARNING
CDM_LOG_FILE=logs/production.log
```

## 配置验证

### 检查当前配置
```bash
python -c "from config import config; 
print(f'Provider: {config.llm_provider}'); 
print(f'Domain: {config.huawei_cloud_domain}'); 
print(f'Mock endpoint: {config.mock_cdm_endpoint}')"
```

### 测试配置有效性
```bash
python test_main_startup.py
```

## 常见问题

### Q: 如何切换LLM提供商？
A: 修改`.env`中的`CDM_LLM_PROVIDER`值，并配置相应的API密钥。

### Q: Mock模式需要真实的华为云账号吗？
A: 不需要，Mock模式使用虚拟数据，任何测试值都可以。

### Q: 如何启用日志文件？
A: 取消注释`CDM_LOG_FILE`配置项并指定文件路径。

### Q: OpenAI API调用失败怎么办？
A: 检查API密钥是否正确，网络是否能访问OpenAI服务。

### Q: Ollama连接失败怎么办？
A: 确保Ollama服务正在运行，地址端口正确。使用诊断工具检查：`python diagnose_ollama.py`

### Q: Ollama模型不匹配怎么办？
A: 1) 运行`ollama list`查看已安装模型；2) 修改`.env`中的`CDM_OLLAMA_MODEL`配置；3) 或者安装配置的模型`ollama pull model_name`

## 安全建议

1. **不要提交API密钥到版本控制**
   - `.env`文件已在`.gitignore`中
   - 使用`.env.example`作为模板

2. **生产环境使用环境变量**
   ```bash
   export CDM_OPENAI_API_KEY="your_key"
   export CDM_HUAWEI_CLOUD_PASSWORD="your_password"
   ```

3. **定期轮换密钥**
   - 华为云密码
   - API密钥

4. **最小权限原则**
   - 华为云账号仅分配必要的CDM权限
   - API密钥使用最小范围

---

💡 **提示**: 建议从测试模式开始，熟悉系统后再配置生产环境。
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试main.py的启动过程，验证MCP错误处理
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_main():
    """测试导入main模块"""
    print("测试导入main模块...")
    
    try:
        import main
        print("[OK] main模块导入成功")
        
        # 检查MCP可用性
        if hasattr(main, 'MCP_AVAILABLE'):
            if main.MCP_AVAILABLE:
                print("[OK] MCP服务器可用")
            else:
                print("[INFO] MCP服务器不可用（这是正常的，如果没有安装MCP库）")
        else:
            print("[WARNING] MCP_AVAILABLE变量未找到")
            
    except ImportError as e:
        print(f"[ERROR] 导入main模块失败: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] 其他错误: {e}")
        return False
    
    return True


def test_interactive_import():
    """测试交互式测试模块导入"""
    print("\n测试交互式测试模块...")
    
    try:
        import interactive_test
        print("[OK] interactive_test模块导入成功")
        
        # 检查CDMAgent类
        agent = interactive_test.CDMAgent()
        print("[OK] CDMAgent实例化成功")
        
    except Exception as e:
        print(f"[ERROR] interactive_test测试失败: {e}")
        return False
    
    return True


def test_mock_integration():
    """测试mock集成"""
    print("\n测试mock集成...")
    
    try:
        from cdm_api.client import CDMClient
        from cdm_api.tools import CDMTools
        
        # 测试mock模式客户端
        client = CDMClient(mock_mode=True)
        print("[OK] Mock模式CDM客户端创建成功")
        
        # 测试工具类
        tools = CDMTools(mock_mode=True)
        print("[OK] Mock模式CDM工具类创建成功")
        
    except Exception as e:
        print(f"[ERROR] Mock集成测试失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 60)
    print("CDM AI Agent 启动测试")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # 测试1: 导入main模块
    if test_import_main():
        tests_passed += 1
    
    # 测试2: 交互式测试
    if test_interactive_import():
        tests_passed += 1
    
    # 测试3: Mock集成
    if test_mock_integration():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("[SUCCESS] 所有测试通过，系统可以正常启动！")
        print("\n可用的启动方式:")
        print("1. python interactive_test.py  # 交互式AI助手")
        print("2. python main.py web         # Web服务器")
        print("3. python main.py mcp         # MCP服务器（需要MCP库）")
        print("4. python cdm_api\\mock_server.py  # Mock API服务器")
    else:
        print("[WARNING] 部分测试失败，请检查依赖和配置")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
"""
使用本地Ollama Qwen2.5:8B模型测试CDM API功能调用
"""
import asyncio
import json
from typing import Dict, List, Any
from loguru import logger

from llm.client import LLMClient
from cdm_api.tools import CDMTools
from config import config

# 配置日志
logger.add("test_ollama_integration.log", rotation="10 MB", level="INFO")


class CDMOllamaAgent:
    """基于Ollama的CDM智能代理"""
    
    def __init__(self, mock_mode: bool = True):
        """
        初始化代理
        
        Args:
            mock_mode: 是否使用MOCK模式
        """
        self.mock_mode = mock_mode
        self.llm_client = LLMClient()
        self.cdm_tools = CDMTools(mock_mode=mock_mode)
        self.conversation_history = []
        
        # 系统提示词
        self.system_prompt = """你是一个华为云CDM（云数据迁移）服务的智能助手。你可以帮助用户管理CDM集群、连接和数据迁移作业。

你的主要功能包括：
1. 查询和管理CDM集群
2. 创建和管理数据库连接
3. 创建和执行数据迁移作业
4. 监控作业状态

当用户提出CDM相关请求时，你应该：
1. 理解用户的需求
2. 选择合适的工具函数来完成任务
3. 清晰地解释操作结果
4. 如果遇到问题，提供解决建议

请用简洁、专业的中文回答用户问题。如果需要调用API，请使用提供的工具函数。
"""

    async def chat(self, user_input: str) -> str:
        """
        与用户进行对话
        
        Args:
            user_input: 用户输入
            
        Returns:
            助手回复
        """
        logger.info(f"用户输入: {user_input}")
        
        # 构建对话消息
        messages = [{"role": "system", "content": self.system_prompt}]
        
        # 添加历史对话
        for msg in self.conversation_history[-10:]:  # 只保留最近10轮对话
            messages.append(msg)
        
        messages.append({"role": "user", "content": user_input})
        
        # 获取工具函数定义
        functions = self.cdm_tools.get_function_definitions()
        
        try:
            # 调用LLM
            response = await self.llm_client.chat(messages, functions=functions)
            logger.info(f"LLM响应: {response}")
            
            # 处理响应
            assistant_message = response.get("content", "")
            
            # 检查是否有函数调用
            if "tool_calls" in response:
                tool_calls = response["tool_calls"]
                
                # 执行函数调用
                function_results = []
                for tool_call in tool_calls:
                    func_name = tool_call["function"]["name"]
                    func_args = json.loads(tool_call["function"]["arguments"])
                    
                    logger.info(f"执行函数: {func_name}, 参数: {func_args}")
                    result = await self.cdm_tools.execute_function(func_name, func_args)
                    function_results.append(result)
                
                # 如果有函数调用结果，生成最终回复
                if function_results:
                    # 添加函数调用结果到对话历史
                    messages.append({
                        "role": "assistant", 
                        "content": f"我将为您执行以下操作: {[f['function']['name'] for f in tool_calls]}"
                    })
                    
                    # 添加函数结果
                    function_summary = []
                    for i, result in enumerate(function_results):
                        func_name = tool_calls[i]["function"]["name"]
                        if result.get("success"):
                            function_summary.append(f"✅ {func_name}: {result.get('message', '执行成功')}")
                        else:
                            function_summary.append(f"❌ {func_name}: {result.get('error', '执行失败')}")
                    
                    messages.append({
                        "role": "user",
                        "content": f"工具执行结果：\\n{chr(10).join(function_summary)}\\n\\n请基于这些结果回复用户。"
                    })
                    
                    # 再次调用LLM生成最终回复
                    final_response = await self.llm_client.chat(messages)
                    assistant_message = final_response.get("content", "操作已完成。")
            
            # 保存对话历史
            self.conversation_history.append({"role": "user", "content": user_input})
            self.conversation_history.append({"role": "assistant", "content": assistant_message})
            
            logger.info(f"助手回复: {assistant_message}")
            return assistant_message
            
        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return f"抱歉，处理您的请求时遇到了问题: {str(e)}"

    def reset_conversation(self):
        """重置对话历史"""
        self.conversation_history = []
        logger.info("对话历史已重置")


async def test_basic_functions():
    """测试基础功能"""
    logger.info("开始测试基础功能...")
    
    agent = CDMOllamaAgent(mock_mode=True)
    
    # 测试用例
    test_cases = [
        "你好，请介绍一下你的功能",
        "查询当前有哪些CDM集群",
        "帮我查看集群 test-cluster-id 的详细信息",
        "获取支持的集群版本列表",
        "查看集群规格列表",
        "查询集群 test-cluster-id 的连接列表",
        "创建一个MySQL连接，名称为test-mysql，连接到192.168.1.100:3306的testdb数据库，用户名root，密码123456，集群ID是test-cluster-id",
        "查询集群 test-cluster-id 的作业列表",
        "创建一个数据迁移作业，从test-mysql连接的users表迁移到test-dws连接的users_backup表，作业名称为migration-job，集群ID是test-cluster-id",
        "启动作业 migration-job，集群ID是test-cluster-id",
        "查询作业 migration-job 的执行状态，集群ID是test-cluster-id"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\\n{'='*50}")
        logger.info(f"测试用例 {i}: {test_case}")
        logger.info(f"{'='*50}")
        
        try:
            response = await agent.chat(test_case)
            print(f"\\n🤖 助手回复:\\n{response}\\n")
            
            # 短暂延迟，避免请求过快
            await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"测试用例 {i} 失败: {e}")
            print(f"\\n❌ 测试用例 {i} 失败: {e}\\n")


async def interactive_mode():
    """交互模式"""
    logger.info("启动交互模式...")
    
    agent = CDMOllamaAgent(mock_mode=True)
    
    print("\\n🚀 CDM智能助手已启动！")
    print("💡 我可以帮您管理华为云CDM服务，包括集群、连接和数据迁移作业")
    print("📝 输入 'quit' 或 'exit' 退出，输入 'reset' 清除对话历史\\n")
    
    while True:
        try:
            user_input = input("👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("\\n👋 再见！")
                break
            elif user_input.lower() in ['reset', '重置']:
                agent.reset_conversation()
                print("\\n🔄 对话历史已清除\\n")
                continue
            elif not user_input:
                continue
            
            print("\\n🤔 正在思考...")
            response = await agent.chat(user_input)
            print(f"\\n🤖 助手: {response}\\n")
            
        except KeyboardInterrupt:
            print("\\n\\n👋 再见！")
            break
        except Exception as e:
            logger.error(f"交互模式错误: {e}")
            print(f"\\n❌ 抱歉，遇到了一个错误: {e}\\n")


async def test_complex_workflow():
    """测试复杂工作流程"""
    logger.info("测试复杂工作流程...")
    
    agent = CDMOllamaAgent(mock_mode=True)
    
    # 复杂场景：完整的数据迁移流程
    workflow_steps = [
        "帮我创建一个CDM集群，名称为data-migration-cluster，使用分片模式，版本2.9.2.200，中等规格，3个节点，部署在cn-north-1a可用区",
        "在刚创建的集群中，创建两个数据库连接：源MySQL连接（名称source-mysql，连接192.168.1.10:3306的production_db，用户名admin，密码secret123）和目标PostgreSQL连接",
        "创建一个数据迁移作业，将source-mysql的orders表迁移到target-postgres的orders_backup表",
        "启动这个迁移作业并监控状态"
    ]
    
    for i, step in enumerate(workflow_steps, 1):
        logger.info(f"\\n{'='*60}")
        logger.info(f"工作流程步骤 {i}: {step}")
        logger.info(f"{'='*60}")
        
        try:
            response = await agent.chat(step)
            print(f"\\n🤖 步骤 {i} 回复:\\n{response}\\n")
            
            await asyncio.sleep(2)  # 稍长的延迟
            
        except Exception as e:
            logger.error(f"工作流程步骤 {i} 失败: {e}")
            print(f"\\n❌ 步骤 {i} 失败: {e}\\n")


async def main():
    """主函数"""
    print("🎯 CDM + Ollama集成测试")
    print("📋 请选择测试模式:")
    print("1. 基础功能测试")
    print("2. 交互模式")
    print("3. 复杂工作流程测试")
    print("4. 全部测试")
    
    choice = input("\\n请输入选项 (1-4): ").strip()
    
    if choice == "1":
        await test_basic_functions()
    elif choice == "2":
        await interactive_mode()
    elif choice == "3":
        await test_complex_workflow()
    elif choice == "4":
        await test_basic_functions()
        print("\\n" + "="*70)
        print("基础测试完成，开始复杂工作流程测试...")
        print("="*70)
        await test_complex_workflow()
    else:
        print("无效选项，默认启动交互模式...")
        await interactive_mode()


if __name__ == "__main__":
    # 首先检查Ollama服务是否可用
    print("🔍 检查Ollama服务状态...")
    
    try:
        import httpx
        import asyncio
        async def check_ollama():
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    qwen_models = [m["name"] for m in models if "qwen" in m["name"].lower()]
                    if qwen_models:
                        print(f"✅ Ollama服务运行正常，找到Qwen模型: {qwen_models}")
                        return True
                    else:
                        print("⚠️ Ollama服务运行中，但未找到Qwen模型")
                        print("请确保已安装qwen2.5:8b模型：ollama pull qwen2.5:8b")
                        return False
                else:
                    print("❌ Ollama服务响应异常")
                    return False
        
        ollama_ok = asyncio.run(check_ollama())
        if not ollama_ok:
            print("\\n请启动Ollama服务并确保qwen2.5:8b模型已安装")
            exit(1)
            
    except Exception as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        print("请确保Ollama已启动并运行在 http://localhost:11434")
        exit(1)
    
    # 检查MOCK服务
    print("\\n🔍 检查CDM MOCK服务状态...")
    try:
        async def check_mock_service():
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/")
                if response.status_code == 200:
                    print("✅ CDM MOCK服务运行正常")
                    return True
                else:
                    return False
        
        mock_ok = asyncio.run(check_mock_service())
        if not mock_ok:
            print("⚠️ CDM MOCK服务未运行，请先启动MOCK服务")
            print("运行命令: python cdm_api/mock_server.py")
            
    except Exception as e:
        print(f"⚠️ 无法连接到CDM MOCK服务: {e}")
        print("请确保MOCK服务已启动: python cdm_api/mock_server.py")
    
    print("\\n🎉 环境检查完成，开始测试...")
    asyncio.run(main())
"""
LLM客户端实现
"""
import json
from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from abc import ABC, abstractmethod

import openai
import anthropic
import tiktoken
import httpx
from loguru import logger

from config import config


class LLMProvider(ABC):
    """LLM提供商抽象基类"""
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: List[Dict[str, str]], 
        functions: Optional[List[Dict]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """聊天完成"""
        pass
    
    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI提供商"""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None, model: str = "gpt-4-turbo"):
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model = model
        
        try:
            self.encoding = tiktoken.encoding_for_model(model)
        except KeyError:
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]], 
        functions: Optional[List[Dict]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """OpenAI聊天完成"""
        try:
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": kwargs.get("temperature", config.temperature),
                "max_tokens": kwargs.get("max_tokens", config.max_tokens),
                "stream": stream
            }
            
            # 添加函数调用参数
            if functions:
                # 转换为OpenAI格式
                tools = []
                for func in functions:
                    tools.append({
                        "type": "function",
                        "function": func
                    })
                params["tools"] = tools
                params["tool_choice"] = "auto"
            
            if stream:
                return self._stream_completion(params)
            else:
                response = await self.client.chat.completions.create(**params)
                return self._parse_response(response)
                
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
    
    async def _stream_completion(self, params):
        """流式完成"""
        async for chunk in await self.client.chat.completions.create(**params):
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
            elif chunk.choices[0].delta.tool_calls:
                yield {"tool_calls": chunk.choices[0].delta.tool_calls}
    
    def _parse_response(self, response) -> Dict:
        """解析响应"""
        choice = response.choices[0]
        message = choice.message
        
        result = {
            "content": message.content,
            "finish_reason": choice.finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
        
        # 处理函数调用
        if message.tool_calls:
            result["tool_calls"] = []
            for tool_call in message.tool_calls:
                result["tool_calls"].append({
                    "id": tool_call.id,
                    "type": tool_call.type,
                    "function": {
                        "name": tool_call.function.name,
                        "arguments": tool_call.function.arguments
                    }
                })
        
        return result
    
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        return len(self.encoding.encode(text))


class AnthropicProvider(LLMProvider):
    """Anthropic提供商"""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        self.client = anthropic.AsyncAnthropic(api_key=api_key)
        self.model = model
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]], 
        functions: Optional[List[Dict]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """Anthropic聊天完成"""
        try:
            # 转换消息格式
            anthropic_messages = self._convert_messages(messages)
            
            params = {
                "model": self.model,
                "messages": anthropic_messages,
                "temperature": kwargs.get("temperature", config.temperature),
                "max_tokens": kwargs.get("max_tokens", config.max_tokens),
                "stream": stream
            }
            
            # 添加工具参数
            if functions:
                params["tools"] = self._convert_functions(functions)
            
            if stream:
                return self._stream_completion(params)
            else:
                response = await self.client.messages.create(**params)
                return self._parse_response(response)
                
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            raise
    
    def _convert_messages(self, messages: List[Dict[str, str]]) -> List[Dict]:
        """转换消息格式"""
        anthropic_messages = []
        system_message = None
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                anthropic_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # Anthropic需要系统消息单独处理
        if system_message and anthropic_messages:
            anthropic_messages[0]["content"] = f"{system_message}\n\n{anthropic_messages[0]['content']}"
        
        return anthropic_messages
    
    def _convert_functions(self, functions: List[Dict]) -> List[Dict]:
        """转换函数格式"""
        tools = []
        for func in functions:
            tools.append({
                "name": func["name"],
                "description": func["description"],
                "input_schema": func["parameters"]
            })
        return tools
    
    async def _stream_completion(self, params):
        """流式完成"""
        async with self.client.messages.stream(**params) as stream:
            async for text in stream.text_stream:
                yield text
    
    def _parse_response(self, response) -> Dict:
        """解析响应"""
        result = {
            "content": response.content[0].text if response.content else "",
            "finish_reason": response.stop_reason,
            "usage": {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            }
        }
        
        # 处理工具使用
        if hasattr(response, 'tool_use') and response.tool_use:
            result["tool_calls"] = []
            for tool_use in response.tool_use:
                result["tool_calls"].append({
                    "id": tool_use.id,
                    "type": "function",
                    "function": {
                        "name": tool_use.name,
                        "arguments": json.dumps(tool_use.input)
                    }
                })
        
        return result
    
    def count_tokens(self, text: str) -> int:
        """计算token数量（近似）"""
        # Anthropic没有公开的tokenizer，使用近似计算
        return len(text) // 4


class OllamaProvider(LLMProvider):
    """Ollama提供商"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "qwen2.5:8b"):
        self.base_url = base_url
        self.model = model
        self.client = httpx.AsyncClient(timeout=300.0)  # 5分钟超时
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]], 
        functions: Optional[List[Dict]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """Ollama聊天完成"""
        try:
            # 转换消息格式为Ollama格式
            prompt = self._convert_messages_to_prompt(messages)
            
            # 如果有函数调用，添加到系统prompt中
            if functions:
                function_desc = self._format_functions_for_prompt(functions)
                prompt = f"{function_desc}\n\n{prompt}"
            
            params = {
                "model": self.model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": kwargs.get("temperature", 0.8),
                    "num_predict": kwargs.get("max_tokens", 2048),
                }
            }
            
            if stream:
                return self._stream_completion(params)
            else:
                response = await self.client.post(
                    f"{self.base_url}/api/generate",
                    json=params
                )
                response.raise_for_status()
                return self._parse_response(response.json())
                
        except Exception as e:
            logger.error(f"Ollama API调用失败: {e}")
            raise
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为单一prompt"""
        prompt_parts = []
        
        for msg in messages:
            role = msg["role"]
            content = msg["content"]
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        # 确保最后以Assistant:开头，让模型继续
        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)
    
    def _format_functions_for_prompt(self, functions: List[Dict]) -> str:
        """将函数格式化为prompt描述"""
        function_descriptions = []
        function_descriptions.append("You have access to the following tools/functions:")
        
        for func in functions:
            name = func["name"]
            description = func["description"]
            parameters = func.get("parameters", {})
            
            func_desc = f"- {name}: {description}"
            if parameters.get("properties"):
                props = []
                for prop_name, prop_info in parameters["properties"].items():
                    prop_type = prop_info.get("type", "string")
                    prop_desc = prop_info.get("description", "")
                    required = prop_name in parameters.get("required", [])
                    req_str = " (required)" if required else " (optional)"
                    props.append(f"  - {prop_name} ({prop_type}){req_str}: {prop_desc}")
                
                if props:
                    func_desc += "\n" + "\n".join(props)
            
            function_descriptions.append(func_desc)
        
        function_descriptions.append("""
To use a function, respond with a JSON object in the following format:
{
  "function_call": {
    "name": "function_name",
    "arguments": {"param1": "value1", "param2": "value2"}
  },
  "reasoning": "Brief explanation of why you're calling this function"
}

If you need to call multiple functions, you can include multiple function_call objects.
If you don't need to call any functions, just respond normally.
""")
        
        return "\n".join(function_descriptions)
    
    async def _stream_completion(self, params):
        """流式完成"""
        async with self.client.stream("POST", f"{self.base_url}/api/generate", json=params) as response:
            response.raise_for_status()
            async for line in response.aiter_lines():
                if line.strip():
                    try:
                        chunk = json.loads(line)
                        if chunk.get("response"):
                            yield chunk["response"]
                    except json.JSONDecodeError:
                        continue
    
    def _parse_response(self, response: Dict) -> Dict:
        """解析Ollama响应"""
        content = response.get("response", "")
        
        result = {
            "content": content,
            "finish_reason": "stop" if response.get("done", False) else "length",
            "usage": {
                "prompt_tokens": response.get("prompt_eval_count", 0),
                "completion_tokens": response.get("eval_count", 0),
                "total_tokens": response.get("prompt_eval_count", 0) + response.get("eval_count", 0)
            }
        }
        
        # 检查是否包含函数调用
        tool_calls = self._extract_function_calls(content)
        if tool_calls:
            result["tool_calls"] = tool_calls
        
        return result
    
    def _extract_function_calls(self, content: str) -> List[Dict]:
        """从响应中提取函数调用"""
        tool_calls = []
        
        try:
            # 尝试解析JSON格式的函数调用
            if "function_call" in content:
                # 找到JSON部分
                start_idx = content.find("{")
                end_idx = content.rfind("}") + 1
                
                if start_idx != -1 and end_idx != -1:
                    json_str = content[start_idx:end_idx]
                    parsed = json.loads(json_str)
                    
                    if "function_call" in parsed:
                        func_call = parsed["function_call"]
                        tool_calls.append({
                            "id": f"call_{len(tool_calls)}",
                            "type": "function",
                            "function": {
                                "name": func_call["name"],
                                "arguments": json.dumps(func_call["arguments"])
                            }
                        })
        
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            logger.debug(f"无法解析函数调用: {e}")
        
        return tool_calls
    
    def count_tokens(self, text: str) -> int:
        """计算token数量（近似）"""
        # Ollama没有公开的tokenizer，使用近似计算
        # 中文大约1个字符=1个token，英文大约4个字符=1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        return chinese_chars + (other_chars // 4)


class LLMClient:
    """LLM客户端"""
    
    def __init__(self):
        self.provider = self._create_provider()
        
    def _create_provider(self) -> LLMProvider:
        """创建LLM提供商"""
        provider_name = config.llm_provider.lower()
        
        if provider_name == "openai":
            if not config.openai_api_key:
                raise ValueError("OpenAI API密钥未配置")
            return OpenAIProvider(
                api_key=config.openai_api_key,
                base_url=config.openai_base_url,
                model=config.openai_model
            )
        
        elif provider_name == "anthropic":
            if not config.anthropic_api_key:
                raise ValueError("Anthropic API密钥未配置")
            return AnthropicProvider(
                api_key=config.anthropic_api_key,
                model=config.anthropic_model
            )
        
        elif provider_name == "ollama":
            return OllamaProvider(
                base_url=getattr(config, 'ollama_base_url', 'http://localhost:11434'),
                model=getattr(config, 'ollama_model', 'qwen2.5:8b')
            )
        
        else:
            raise ValueError(f"不支持的LLM提供商: {provider_name}")
    
    async def chat(
        self,
        messages: List[Dict[str, str]], 
        functions: Optional[List[Dict]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict, AsyncGenerator]:
        """聊天对话"""
        return await self.provider.chat_completion(messages, functions, stream, **kwargs)
    
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        return self.provider.count_tokens(text)
    
    def truncate_messages(self, messages: List[Dict[str, str]], max_tokens: int) -> List[Dict[str, str]]:
        """截断消息以适应token限制"""
        if not messages:
            return messages
        
        # 保留系统消息和最后几条消息
        system_messages = [msg for msg in messages if msg["role"] == "system"]
        other_messages = [msg for msg in messages if msg["role"] != "system"]
        
        truncated = system_messages.copy()
        current_tokens = sum(self.count_tokens(msg["content"]) for msg in system_messages)
        
        # 从后往前添加消息
        for message in reversed(other_messages):
            msg_tokens = self.count_tokens(message["content"])
            if current_tokens + msg_tokens > max_tokens:
                break
            truncated.append(message)
            current_tokens += msg_tokens
        
        # 恢复正确顺序（除了system消息）
        if len(truncated) > len(system_messages):
            user_assistant_msgs = truncated[len(system_messages):]
            user_assistant_msgs.reverse()
            truncated = system_messages + user_assistant_msgs
        
        return truncated
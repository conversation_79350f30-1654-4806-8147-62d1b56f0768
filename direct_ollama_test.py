# -*- coding: utf-8 -*-
"""
直接测试Ollama API连接
"""
import asyncio
import json
import httpx

async def test_ollama_direct():
    """直接测试Ollama API"""
    print("Testing direct Ollama API...")
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # 测试基本生成
            params = {
                "model": "qwen3:8b",
                "prompt": "Hello, please introduce yourself in Chinese.",
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 100,
                }
            }
            
            print("Sending request to Ollama...")
            response = await client.post(
                "http://localhost:11434/api/generate",
                json=params,
                timeout=60.0
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result.get('response', '')}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"Error: {e}")
        return False

async def test_ollama_chat():
    """测试Ollama Chat API"""
    print("\nTesting Ollama Chat API...")
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            params = {
                "model": "qwen3:8b",
                "messages": [
                    {"role": "user", "content": "Hello, introduce yourself in Chinese"}
                ],
                "stream": False
            }
            
            print("Sending chat request to Ollama...")
            response = await client.post(
                "http://localhost:11434/api/chat",
                json=params,
                timeout=60.0
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                message = result.get('message', {})
                content = message.get('content', '')
                print(f"Response: {content}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"Error: {e}")
        return False

async def main():
    print("Direct Ollama API Test")
    print("=" * 40)
    
    # 测试生成API
    generate_ok = await test_ollama_direct()
    
    # 测试聊天API  
    chat_ok = await test_ollama_chat()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"Generate API: {'PASSED' if generate_ok else 'FAILED'}")
    print(f"Chat API: {'PASSED' if chat_ok else 'FAILED'}")
    
    if generate_ok or chat_ok:
        print("\nOllama is working! We can proceed with integration.")
    else:
        print("\nOllama API test failed.")

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ollama配置诊断工具
"""
import asyncio
import sys
import os
import json
from typing import Dict, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import httpx
from config import config


async def check_ollama_service():
    """检查Ollama服务状态"""
    print("=" * 60)
    print("Ollama服务诊断")
    print("=" * 60)
    
    # 检查服务是否运行
    print(f"\n1. 检查Ollama服务 ({config.ollama_base_url})")
    try:
        async with httpx.AsyncClient(timeout=5) as client:
            response = await client.get(f"{config.ollama_base_url}/api/version")
            if response.status_code == 200:
                version_data = response.json()
                print(f"[OK] Ollama服务正在运行")
                print(f"    版本: {version_data.get('version', '未知')}")
            else:
                print(f"[ERROR] Ollama响应异常: {response.status_code}")
                return False
    except Exception as e:
        print(f"[ERROR] 无法连接到Ollama服务: {e}")
        print(f"    请确保Ollama已安装并运行:")
        print(f"    - 安装: https://ollama.ai/")
        print(f"    - 启动: ollama serve")
        return False
    
    # 检查可用模型
    print(f"\n2. 检查可用模型")
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(f"{config.ollama_base_url}/api/tags")
            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get('models', [])
                
                if models:
                    print(f"[OK] 发现 {len(models)} 个已安装模型:")
                    for model in models:
                        name = model.get('name', '未知')
                        size = model.get('size', 0)
                        size_gb = size / (1024**3)
                        print(f"    - {name} ({size_gb:.1f}GB)")
                else:
                    print(f"[WARNING] 没有找到已安装的模型")
                    print(f"    请安装一个模型，例如:")
                    print(f"    ollama pull qwen2.5:8b")
                    return False
            else:
                print(f"[ERROR] 无法获取模型列表: {response.status_code}")
                return False
    except Exception as e:
        print(f"[ERROR] 获取模型列表失败: {e}")
        return False
    
    # 检查配置的模型
    print(f"\n3. 检查配置模型: {config.ollama_model}")
    configured_model = config.ollama_model
    model_names = [model.get('name', '') for model in models]
    
    if configured_model in model_names:
        print(f"[OK] 配置的模型 '{configured_model}' 已安装")
    else:
        print(f"[ERROR] 配置的模型 '{configured_model}' 未安装")
        print(f"    请选择以下已安装的模型之一:")
        for name in model_names:
            print(f"    - {name}")
        print(f"\n    修改.env文件中的CDM_OLLAMA_MODEL配置")
        return False
    
    return True


async def test_ollama_chat():
    """测试Ollama聊天功能"""
    print(f"\n4. 测试Ollama聊天功能")
    
    try:
        from llm.client import LLMClient
        client = LLMClient()
        
        test_messages = [
            {"role": "user", "content": "Hello, please respond with 'OK' if you can understand me."}
        ]
        
        print(f"    发送测试消息...")
        response = await client.chat(test_messages)
        
        if response and response.get('content'):
            content = response['content'][:100]
            print(f"[OK] LLM响应成功")
            print(f"    响应内容: {content}...")
            return True
        else:
            print(f"[ERROR] LLM响应为空")
            return False
            
    except Exception as e:
        print(f"[ERROR] LLM测试失败: {e}")
        return False


async def suggest_fixes():
    """提供修复建议"""
    print(f"\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    print(f"\n如果遇到问题，请尝试以下解决方案:")
    
    print(f"\n1. Ollama服务未运行:")
    print(f"   - Windows: 从开始菜单启动Ollama")
    print(f"   - 命令行: ollama serve")
    print(f"   - 检查端口: netstat -an | findstr 11434")
    
    print(f"\n2. 模型未安装:")
    print(f"   ollama pull qwen2.5:8b")
    print(f"   ollama pull llama2")
    print(f"   ollama pull codellama")
    
    print(f"\n3. 模型名称不匹配:")
    print(f"   - 运行: ollama list")
    print(f"   - 修改.env中的CDM_OLLAMA_MODEL配置")
    
    print(f"\n4. 端口冲突:")
    print(f"   - 修改.env中的CDM_OLLAMA_BASE_URL")
    print(f"   - 或者更换Ollama服务端口")
    
    print(f"\n5. 替代LLM提供商:")
    print(f"   - OpenAI: 设置CDM_LLM_PROVIDER=openai")
    print(f"   - Anthropic: 设置CDM_LLM_PROVIDER=anthropic")


async def main():
    """主诊断函数"""
    print("Ollama配置诊断工具")
    print(f"当前配置:")
    print(f"  LLM提供商: {config.llm_provider}")
    print(f"  Ollama地址: {config.ollama_base_url}")
    print(f"  Ollama模型: {config.ollama_model}")
    
    if config.llm_provider != 'ollama':
        print(f"\n[INFO] 当前使用的是 {config.llm_provider} 提供商，不是ollama")
        print(f"如需测试Ollama，请在.env中设置: CDM_LLM_PROVIDER=ollama")
        return
    
    # 运行诊断
    service_ok = await check_ollama_service()
    
    if service_ok:
        chat_ok = await test_ollama_chat()
        
        if chat_ok:
            print(f"\n" + "=" * 60)
            print(f"[SUCCESS] Ollama配置正常，可以开始使用!")
            print(f"建议的使用方式:")
            print(f"  python interactive_test.py")
            print(f"=" * 60)
        else:
            await suggest_fixes()
    else:
        await suggest_fixes()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n诊断被用户中断")
    except Exception as e:
        print(f"诊断过程中出现错误: {e}")
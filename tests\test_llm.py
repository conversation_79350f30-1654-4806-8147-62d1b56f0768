"""
测试LLM集成模块
"""
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock
from llm.client import LL<PERSON><PERSON>, OpenAIProvider, AnthropicProvider
from llm.function_calling import FunctionCallHandler, FunctionCall, FunctionResult
from llm.prompt_manager import PromptManager


class TestLLMClient:
    """测试LLM客户端"""
    
    def test_create_openai_provider(self):
        """测试创建OpenAI提供商"""
        # 模拟配置
        with pytest.raises(ValueError, match="OpenAI API密钥未配置"):
            client = LLMClient()
    
    def test_count_tokens(self):
        """测试Token计数"""
        # 由于需要API密钥，这里只测试接口
        pass


class TestFunctionCallHandler:
    """测试Function Calling处理器"""
    
    def setup_method(self):
        # 创建模拟的CDM工具
        self.mock_tools = Mock()
        self.mock_tools.get_tool_definitions.return_value = [
            Mock(name="cdm_list_clusters", description="列出集群", inputSchema={})
        ]
        self.handler = FunctionCallHandler(self.mock_tools)
    
    def test_parse_function_calls(self):
        """测试解析函数调用"""
        tool_calls = [{
            "id": "call_123",
            "function": {
                "name": "cdm_list_clusters",
                "arguments": "{}"
            }
        }]
        
        calls = self.handler.parse_function_calls(tool_calls)
        assert len(calls) == 1
        assert calls[0].name == "cdm_list_clusters"
        assert calls[0].id == "call_123"
    
    def test_validate_function_call(self):
        """测试验证函数调用"""
        call = FunctionCall(
            id="call_123",
            name="unknown_function", 
            arguments={}
        )
        
        error = self.handler.validate_function_call(call)
        assert "未知函数" in error


class TestPromptManager:
    """测试Prompt管理器"""
    
    def setup_method(self):
        self.prompt_manager = PromptManager()
    
    def test_create_system_message(self):
        """测试创建系统消息"""
        msg = self.prompt_manager.create_system_message()
        assert msg["role"] == "system"
        assert "CDM" in msg["content"]
        assert "华为云" in msg["content"]
    
    def test_create_user_message(self):
        """测试创建用户消息"""
        msg = self.prompt_manager.create_user_message("列出集群")
        assert msg["role"] == "user"
        assert msg["content"] == "列出集群"
    
    def test_create_user_message_with_context(self):
        """测试带上下文的用户消息"""
        context = {"current_cluster_id": "cluster-123"}
        msg = self.prompt_manager.create_user_message("查看状态", context)
        assert msg["role"] == "user"
        assert "cluster-123" in msg["content"]
    
    def test_create_help_prompt(self):
        """测试创建帮助提示"""
        help_text = self.prompt_manager.create_help_prompt()
        assert "集群管理" in help_text
        assert "连接管理" in help_text
        assert "作业管理" in help_text
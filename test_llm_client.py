# -*- coding: utf-8 -*-
"""
测试LLM客户端与Ollama的集成
"""
import asyncio
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm.client import OllamaProvider

async def test_ollama_provider():
    """测试OllamaProvider"""
    print("Testing OllamaProvider...")
    
    try:
        # 创建Ollama provider实例
        provider = OllamaProvider(
            base_url="http://localhost:11434",
            model="qwen3:8b"
        )
        
        # 测试消息
        messages = [
            {"role": "system", "content": "你是一个AI助手，请用中文简洁回答问题。"},
            {"role": "user", "content": "你好，请简单介绍一下你自己。"}
        ]
        
        print("Sending request...")
        response = await provider.chat_completion(messages)
        
        print(f"Response type: {type(response)}")
        print(f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        if isinstance(response, dict) and 'content' in response:
            content = response['content']
            print(f"Content: {content[:200]}...")  # 只显示前200个字符
            return True
        else:
            print(f"Unexpected response format: {response}")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_function_calling():
    """测试函数调用"""
    print("\nTesting function calling...")
    
    try:
        provider = OllamaProvider(
            base_url="http://localhost:11434", 
            model="qwen3:8b"
        )
        
        functions = [{
            "name": "get_weather",
            "description": "获取指定城市的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        }]
        
        messages = [
            {"role": "system", "content": "你是一个AI助手。当用户询问天气时，使用get_weather函数。"},
            {"role": "user", "content": "北京今天天气怎么样？"}
        ]
        
        print("Sending function call request...")
        response = await provider.chat_completion(messages, functions=functions)
        
        print(f"Response: {response}")
        
        if 'tool_calls' in response:
            print("Function calling detected!")
            return True
        else:
            print("No function calls detected")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

async def main():
    print("LLM Client Ollama Integration Test")
    print("=" * 50)
    
    # 测试基本对话
    basic_ok = await test_ollama_provider()
    
    # 测试函数调用
    function_ok = await test_function_calling()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Basic Chat: {'PASSED' if basic_ok else 'FAILED'}")
    print(f"Function Calling: {'PASSED' if function_ok else 'FAILED'}")
    
    return basic_ok

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
"""
CDM API工具函数定义
用于大模型函数调用
"""
from typing import Dict, List, Any, Optional
import json
from loguru import logger

from .client import CDMClient
from .models import (
    CreateClusterRequest, CreateConnectionRequest, CreateJobRequest,
    Connection, Job, ConfigInput, ConfigSection, LinkConfigValues
)
from .exceptions import CDMError


class CDMTools:
    """CDM API工具类，提供大模型可调用的函数"""
    
    def __init__(self, mock_mode: bool = True):
        """
        初始化CDM工具
        
        Args:
            mock_mode: 是否使用MOCK模式（默认True，避免真实API调用）
        """
        self.mock_mode = mock_mode
        if mock_mode:
            # 使用MOCK服务
            self.client = CDMClient(
                username="mock_user",
                password="mock_password",
                domain_name="mock_domain",
                project_id="test-project-id",
                cdm_endpoint="http://localhost:8000",
                mock_mode=True
            )
        else:
            # 使用真实服务（需要配置真实凭据）
            self.client = CDMClient(mock_mode=False)
    
    def get_function_definitions(self) -> List[Dict]:
        """获取函数定义列表，用于大模型函数调用"""
        return [
            {
                "name": "list_clusters",
                "description": "查询CDM集群列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "page": {
                            "type": "integer",
                            "description": "页码，默认1",
                            "default": 1
                        },
                        "size": {
                            "type": "integer", 
                            "description": "每页数量，默认10",
                            "default": 10
                        }
                    }
                }
            },
            {
                "name": "get_cluster",
                "description": "查询指定集群的详细信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "create_cluster",
                "description": "创建新的CDM集群",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "集群名称"
                        },
                        "mode": {
                            "type": "string",
                            "description": "集群模式，可选值：sharding（分片）、simple（简单）",
                            "enum": ["sharding", "simple"]
                        },
                        "version": {
                            "type": "string",
                            "description": "CDM版本，如 *********"
                        },
                        "flavor_id": {
                            "type": "string", 
                            "description": "规格ID，如 cdm.medium"
                        },
                        "num_instances": {
                            "type": "integer",
                            "description": "节点数量"
                        },
                        "availability_zone": {
                            "type": "string",
                            "description": "可用区，如 cn-north-1a"
                        },
                        "vpc_id": {
                            "type": "string",
                            "description": "VPC ID"
                        },
                        "subnet_id": {
                            "type": "string",
                            "description": "子网ID"
                        },
                        "security_group_id": {
                            "type": "string",
                            "description": "安全组ID"
                        }
                    },
                    "required": ["name", "mode", "version", "flavor_id", "num_instances", 
                               "availability_zone", "vpc_id", "subnet_id", "security_group_id"]
                }
            },
            {
                "name": "list_connections",
                "description": "查询指定集群的连接列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "create_mysql_connection",
                "description": "创建MySQL数据库连接",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        },
                        "host": {
                            "type": "string",
                            "description": "MySQL主机地址"
                        },
                        "port": {
                            "type": "string",
                            "description": "MySQL端口，默认3306",
                            "default": "3306"
                        },
                        "database": {
                            "type": "string",
                            "description": "数据库名称"
                        },
                        "username": {
                            "type": "string",
                            "description": "用户名"
                        },
                        "password": {
                            "type": "string",
                            "description": "密码"
                        }
                    },
                    "required": ["cluster_id", "connection_name", "host", "database", "username", "password"]
                }
            },
            {
                "name": "list_jobs",
                "description": "查询指定集群的作业列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "create_data_migration_job",
                "description": "创建数据迁移作业",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        },
                        "source_connection": {
                            "type": "string",
                            "description": "源连接名称"
                        },
                        "target_connection": {
                            "type": "string", 
                            "description": "目标连接名称"
                        },
                        "source_table": {
                            "type": "string",
                            "description": "源表名称"
                        },
                        "target_table": {
                            "type": "string",
                            "description": "目标表名称"
                        },
                        "source_schema": {
                            "type": "string",
                            "description": "源数据库/模式名称，可选",
                            "default": ""
                        },
                        "target_schema": {
                            "type": "string",
                            "description": "目标数据库/模式名称，可选", 
                            "default": ""
                        }
                    },
                    "required": ["cluster_id", "job_name", "source_connection", "target_connection", 
                               "source_table", "target_table"]
                }
            },
            {
                "name": "start_job",
                "description": "启动指定的数据迁移作业",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "get_job_status",
                "description": "查询作业执行状态",
                "parameters": {
                    "type": "object", 
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "get_cluster_versions",
                "description": "获取支持的CDM集群版本列表",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            {
                "name": "get_cluster_flavors", 
                "description": "获取可用的集群规格列表",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            {
                "name": "delete_cluster",
                "description": "删除指定的CDM集群",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "restart_cluster",
                "description": "重启指定的CDM集群",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "start_cluster",
                "description": "启动指定的CDM集群",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "stop_cluster",
                "description": "停止指定的CDM集群",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        }
                    },
                    "required": ["cluster_id"]
                }
            },
            {
                "name": "get_connection",
                "description": "查询指定连接的详细信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        }
                    },
                    "required": ["cluster_id", "connection_name"]
                }
            },
            {
                "name": "delete_connection",
                "description": "删除指定的连接",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        }
                    },
                    "required": ["cluster_id", "connection_name"]
                }
            },
            {
                "name": "get_job",
                "description": "查询指定作业的详细信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "delete_job",
                "description": "删除指定的作业",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "stop_job",
                "description": "停止正在执行的作业",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "get_job_executions",
                "description": "查询作业的执行历史记录",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "job_name": {
                            "type": "string",
                            "description": "作业名称"
                        }
                    },
                    "required": ["cluster_id", "job_name"]
                }
            },
            {
                "name": "update_connection",
                "description": "更新连接配置",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "connection_name": {
                            "type": "string",
                            "description": "连接名称"
                        },
                        "host": {
                            "type": "string", 
                            "description": "数据库主机地址"
                        },
                        "port": {
                            "type": "string",
                            "description": "数据库端口，默认3306",
                            "default": "3306"
                        },
                        "database": {
                            "type": "string",
                            "description": "数据库名称"
                        },
                        "username": {
                            "type": "string",
                            "description": "用户名"
                        },
                        "password": {
                            "type": "string",
                            "description": "密码"
                        }
                    },
                    "required": ["cluster_id", "connection_name"]
                }
            },
            {
                "name": "wait_cluster_ready",
                "description": "等待集群就绪（用于集群创建后的状态检查）",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "cluster_id": {
                            "type": "string",
                            "description": "集群ID"
                        },
                        "timeout": {
                            "type": "integer",
                            "description": "超时时间（秒），默认1800秒（30分钟）",
                            "default": 1800
                        }
                    },
                    "required": ["cluster_id"]
                }
            }
        ]
    
    async def execute_function(self, function_name: str, arguments: Dict) -> Dict[str, Any]:
        """
        执行指定的函数
        
        Args:
            function_name: 函数名称
            arguments: 函数参数
            
        Returns:
            函数执行结果
        """
        try:
            logger.info(f"执行CDM函数: {function_name}，参数: {arguments}")
            
            if function_name == "list_clusters":
                return await self._list_clusters(**arguments)
            elif function_name == "get_cluster":
                return await self._get_cluster(**arguments)
            elif function_name == "create_cluster":
                return await self._create_cluster(**arguments)
            elif function_name == "list_connections":
                return await self._list_connections(**arguments)
            elif function_name == "create_mysql_connection":
                return await self._create_mysql_connection(**arguments)
            elif function_name == "list_jobs":
                return await self._list_jobs(**arguments)
            elif function_name == "create_data_migration_job":
                return await self._create_data_migration_job(**arguments)
            elif function_name == "start_job":
                return await self._start_job(**arguments)
            elif function_name == "get_job_status":
                return await self._get_job_status(**arguments)
            elif function_name == "get_cluster_versions":
                return await self._get_cluster_versions()
            elif function_name == "get_cluster_flavors":
                return await self._get_cluster_flavors()
            elif function_name == "delete_cluster":
                return await self._delete_cluster(**arguments)
            elif function_name == "restart_cluster":
                return await self._restart_cluster(**arguments)
            elif function_name == "start_cluster":
                return await self._start_cluster(**arguments)
            elif function_name == "stop_cluster":
                return await self._stop_cluster(**arguments)
            elif function_name == "get_connection":
                return await self._get_connection(**arguments)
            elif function_name == "delete_connection":
                return await self._delete_connection(**arguments)
            elif function_name == "get_job":
                return await self._get_job(**arguments)
            elif function_name == "delete_job":
                return await self._delete_job(**arguments)
            elif function_name == "stop_job":
                return await self._stop_job(**arguments)
            elif function_name == "get_job_executions":
                return await self._get_job_executions(**arguments)
            elif function_name == "update_connection":
                return await self._update_connection(**arguments)
            elif function_name == "wait_cluster_ready":
                return await self._wait_cluster_ready(**arguments)
            else:
                return {"error": f"未知函数: {function_name}"}
                
        except Exception as e:
            logger.error(f"执行函数 {function_name} 失败: {e}")
            return {"error": str(e)}
    
    async def _list_clusters(self, page: int = 1, size: int = 10) -> Dict:
        """查询集群列表"""
        result = await self.client.list_clusters(page=page, size=size)
        return {
            "success": True,
            "message": f"成功获取集群列表，共 {result.total} 个集群",
            "data": {
                "total": result.total,
                "clusters": [cluster.dict() for cluster in result.items]
            }
        }
    
    async def _get_cluster(self, cluster_id: str) -> Dict:
        """查询集群详情"""
        cluster = await self.client.get_cluster(cluster_id)
        return {
            "success": True,
            "message": f"成功获取集群 {cluster_id} 的详细信息",
            "data": cluster.dict()
        }
    
    async def _create_cluster(self, **kwargs) -> Dict:
        """创建集群"""
        request = CreateClusterRequest.create_cluster_config(**kwargs)
        cluster_id = await self.client.create_cluster(request)
        return {
            "success": True,
            "message": f"成功创建集群，集群ID: {cluster_id}",
            "data": {"cluster_id": cluster_id}
        }
    
    async def _list_connections(self, cluster_id: str) -> Dict:
        """查询连接列表"""
        connections = await self.client.list_connections(cluster_id)
        return {
            "success": True,
            "message": f"成功获取集群 {cluster_id} 的连接列表，共 {len(connections)} 个连接",
            "data": {"connections": [conn.dict() for conn in connections]}
        }
    
    async def _create_mysql_connection(self, cluster_id: str, connection_name: str, 
                                     host: str, database: str, username: str, 
                                     password: str, port: str = "3306") -> Dict:
        """创建MySQL连接"""
        connection = Connection(
            name=connection_name,
            connector_name="generic-jdbc-connector",
            link_config_values=LinkConfigValues(
                configs=[
                    ConfigSection(
                        name="linkConfig",
                        inputs=[
                            ConfigInput(name="linkConfig.databaseType", value="MYSQL"),
                            ConfigInput(name="linkConfig.host", value=host),
                            ConfigInput(name="linkConfig.port", value=port),
                            ConfigInput(name="linkConfig.database", value=database),
                            ConfigInput(name="linkConfig.username", value=username),
                            ConfigInput(name="linkConfig.password", value=password)
                        ]
                    )
                ]
            )
        )
        
        request = CreateConnectionRequest(links=[connection])
        await self.client.create_connection(cluster_id, request)
        
        return {
            "success": True,
            "message": f"成功创建MySQL连接: {connection_name}",
            "data": {"connection_name": connection_name, "host": host, "database": database}
        }
    
    async def _list_jobs(self, cluster_id: str) -> Dict:
        """查询作业列表"""
        jobs = await self.client.list_jobs(cluster_id)
        return {
            "success": True,
            "message": f"成功获取集群 {cluster_id} 的作业列表，共 {len(jobs)} 个作业",
            "data": {"jobs": [job.dict() for job in jobs]}
        }
    
    async def _create_data_migration_job(self, cluster_id: str, job_name: str,
                                       source_connection: str, target_connection: str,
                                       source_table: str, target_table: str,
                                       source_schema: str = "", target_schema: str = "") -> Dict:
        """创建数据迁移作业"""
        
        # 构造源配置
        source_inputs = [
            ConfigInput(name="fromJobConfig.useSql", value="false"),
            ConfigInput(name="fromJobConfig.tableName", value=source_table),
        ]
        if source_schema:
            source_inputs.append(ConfigInput(name="fromJobConfig.schemaName", value=source_schema))
        
        # 构造目标配置
        target_inputs = [
            ConfigInput(name="toJobConfig.tablePreparation", value="DROP_AND_CREATE"),
            ConfigInput(name="toJobConfig.tableName", value=target_table),
        ]
        if target_schema:
            target_inputs.append(ConfigInput(name="toJobConfig.schemaName", value=target_schema))
        
        job = Job(
            name=job_name,
            type="NORMAL_JOB",
            from_connector_name="generic-jdbc-connector",
            to_connector_name="generic-jdbc-connector", 
            from_link_name=source_connection,
            to_link_name=target_connection,
            from_config_values=LinkConfigValues(
                configs=[ConfigSection(name="fromJobConfig", inputs=source_inputs)]
            ),
            to_config_values=LinkConfigValues(
                configs=[ConfigSection(name="toJobConfig", inputs=target_inputs)]
            )
        )
        
        request = CreateJobRequest(jobs=[job])
        await self.client.create_job(cluster_id, request)
        
        return {
            "success": True,
            "message": f"成功创建数据迁移作业: {job_name}",
            "data": {
                "job_name": job_name,
                "source_connection": source_connection,
                "target_connection": target_connection,
                "source_table": source_table,
                "target_table": target_table
            }
        }
    
    async def _start_job(self, cluster_id: str, job_name: str) -> Dict:
        """启动作业"""
        response = await self.client.start_job(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功启动作业: {job_name}",
            "data": {"submission_id": response.submission_id}
        }
    
    async def _get_job_status(self, cluster_id: str, job_name: str) -> Dict:
        """查询作业状态"""
        status = await self.client.get_job_status(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功获取作业 {job_name} 的状态",
            "data": status
        }
    
    async def _get_cluster_versions(self) -> Dict:
        """获取集群版本列表"""
        versions = await self.client.get_cluster_versions()
        return {
            "success": True,
            "message": "成功获取支持的集群版本列表",
            "data": {"versions": versions}
        }
    
    async def _get_cluster_flavors(self) -> Dict:
        """获取集群规格列表"""
        flavors = await self.client.get_cluster_flavors()
        return {
            "success": True,
            "message": "成功获取集群规格列表",
            "data": {"flavors": flavors}
        }
    
    async def _delete_cluster(self, cluster_id: str) -> Dict:
        """删除集群"""
        await self.client.delete_cluster(cluster_id)
        return {
            "success": True,
            "message": f"成功删除集群: {cluster_id}",
            "data": {"cluster_id": cluster_id}
        }
    
    async def _restart_cluster(self, cluster_id: str) -> Dict:
        """重启集群"""
        await self.client.restart_cluster(cluster_id)
        return {
            "success": True,
            "message": f"成功发送重启命令到集群: {cluster_id}",
            "data": {"cluster_id": cluster_id, "action": "restart"}
        }
    
    async def _start_cluster(self, cluster_id: str) -> Dict:
        """启动集群"""
        await self.client.start_cluster(cluster_id)
        return {
            "success": True,
            "message": f"成功发送启动命令到集群: {cluster_id}",
            "data": {"cluster_id": cluster_id, "action": "start"}
        }
    
    async def _stop_cluster(self, cluster_id: str) -> Dict:
        """停止集群"""
        await self.client.stop_cluster(cluster_id)
        return {
            "success": True,
            "message": f"成功发送停止命令到集群: {cluster_id}",
            "data": {"cluster_id": cluster_id, "action": "stop"}
        }
    
    async def _get_connection(self, cluster_id: str, connection_name: str) -> Dict:
        """获取连接详情"""
        connection = await self.client.get_connection(cluster_id, connection_name)
        return {
            "success": True,
            "message": f"成功获取连接 {connection_name} 的详细信息",
            "data": connection.dict()
        }
    
    async def _delete_connection(self, cluster_id: str, connection_name: str) -> Dict:
        """删除连接"""
        await self.client.delete_connection(cluster_id, connection_name)
        return {
            "success": True,
            "message": f"成功删除连接: {connection_name}",
            "data": {"cluster_id": cluster_id, "connection_name": connection_name}
        }
    
    async def _get_job(self, cluster_id: str, job_name: str) -> Dict:
        """获取作业详情"""
        job = await self.client.get_job(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功获取作业 {job_name} 的详细信息",
            "data": job.dict()
        }
    
    async def _delete_job(self, cluster_id: str, job_name: str) -> Dict:
        """删除作业"""
        await self.client.delete_job(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功删除作业: {job_name}",
            "data": {"cluster_id": cluster_id, "job_name": job_name}
        }
    
    async def _stop_job(self, cluster_id: str, job_name: str) -> Dict:
        """停止作业"""
        await self.client.stop_job(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功停止作业: {job_name}",
            "data": {"cluster_id": cluster_id, "job_name": job_name}
        }
    
    async def _get_job_executions(self, cluster_id: str, job_name: str) -> Dict:
        """获取作业执行历史"""
        executions = await self.client.get_job_executions(cluster_id, job_name)
        return {
            "success": True,
            "message": f"成功获取作业 {job_name} 的执行历史，共 {len(executions)} 条记录",
            "data": {"executions": [execution.dict() for execution in executions]}
        }
    
    async def _update_connection(self, cluster_id: str, connection_name: str, **kwargs) -> Dict:
        """更新连接配置"""
        from .models import Connection, ConfigInput, ConfigSection, LinkConfigValues
        
        # 构建更新的连接配置
        inputs = []
        if 'host' in kwargs and kwargs['host']:
            inputs.append(ConfigInput(name="linkConfig.host", value=kwargs['host']))
        if 'port' in kwargs and kwargs['port']:
            inputs.append(ConfigInput(name="linkConfig.port", value=kwargs['port']))
        if 'database' in kwargs and kwargs['database']:
            inputs.append(ConfigInput(name="linkConfig.database", value=kwargs['database']))
        if 'username' in kwargs and kwargs['username']:
            inputs.append(ConfigInput(name="linkConfig.username", value=kwargs['username']))
        if 'password' in kwargs and kwargs['password']:
            inputs.append(ConfigInput(name="linkConfig.password", value=kwargs['password']))
        
        # 如果没有提供更新参数，只是获取现有连接信息
        if not inputs:
            connection = await self.client.get_connection(cluster_id, connection_name)
        else:
            # 构建连接对象
            connection = Connection(
                name=connection_name,
                connector_name="generic-jdbc-connector",
                link_config_values=LinkConfigValues(
                    configs=[ConfigSection(name="linkConfig", inputs=inputs)]
                )
            )
            
        await self.client.update_connection(cluster_id, connection_name, connection)
        return {
            "success": True,
            "message": f"成功更新连接: {connection_name}",
            "data": {
                "cluster_id": cluster_id,
                "connection_name": connection_name,
                "updated_fields": list(kwargs.keys())
            }
        }
    
    async def _wait_cluster_ready(self, cluster_id: str, timeout: int = 1800) -> Dict:
        """等待集群就绪"""
        is_ready = await self.client.wait_cluster_ready(cluster_id, timeout)
        if is_ready:
            return {
                "success": True,
                "message": f"集群 {cluster_id} 已就绪",
                "data": {"cluster_id": cluster_id, "status": "ready", "timeout": timeout}
            }
        else:
            return {
                "success": False,
                "message": f"集群 {cluster_id} 在 {timeout} 秒内未就绪",
                "data": {"cluster_id": cluster_id, "status": "timeout", "timeout": timeout}
            }